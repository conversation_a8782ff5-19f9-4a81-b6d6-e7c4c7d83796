"use client";import{useFocusRing as Te}from"@react-aria/focus";import{useHover as fe}from"@react-aria/interactions";import x,{Fragment as ee,useCallback as k,useEffect as ye,useMemo as K,useRef as Q,useState as Pe}from"react";import{flushSync as $}from"react-dom";import{useActivePress as Ee}from'../../hooks/use-active-press.js';import{useDidElementMove as ge}from'../../hooks/use-did-element-move.js';import{useDisposables as Me}from'../../hooks/use-disposables.js';import{useElementSize as be}from'../../hooks/use-element-size.js';import{useEvent as P}from'../../hooks/use-event.js';import{useId as B}from'../../hooks/use-id.js';import{useInertOthers as Ae}from'../../hooks/use-inert-others.js';import{useIsoMorphicEffect as W}from'../../hooks/use-iso-morphic-effect.js';import{useOnDisappear as _e}from'../../hooks/use-on-disappear.js';import{useOutsideClick as Ie}from'../../hooks/use-outside-click.js';import{useOwnerDocument as te}from'../../hooks/use-owner.js';import{Action as J,useQuickRelease as Se}from'../../hooks/use-quick-release.js';import{useResolveButtonType as Re}from'../../hooks/use-resolve-button-type.js';import{useScrollLock as De}from'../../hooks/use-scroll-lock.js';import{useSyncRefs as V}from'../../hooks/use-sync-refs.js';import{useTextValue as Fe}from'../../hooks/use-text-value.js';import{useTrackedPointer as he}from'../../hooks/use-tracked-pointer.js';import{transitionDataAttributes as xe,useTransition as Ce}from'../../hooks/use-transition.js';import{useTreeWalker as Le}from'../../hooks/use-tree-walker.js';import{FloatingProvider as ve,useFloatingPanel as Oe,useFloatingPanelProps as He,useFloatingReference as Ue,useFloatingReferenceProps as Ge,useResolvedAnchor as Ne}from'../../internal/floating.js';import{OpenClosedProvider as ke,State as X,useOpenClosed as Be}from'../../internal/open-closed.js';import{stackMachines as we}from'../../machines/stack-machine.js';import{useSlice as D}from'../../react-glue.js';import{isDisabledReactIssue7711 as Ke}from'../../utils/bugs.js';import{Focus as A}from'../../utils/calculate-active-index.js';import{disposables as We}from'../../utils/disposables.js';import*as Je from'../../utils/dom.js';import{Focus as oe,FocusableMode as Ve,focusFrom as Xe,isFocusableElement as Qe,restoreFocusIfNecessary as ne}from'../../utils/focus-management.js';import{match as $e}from'../../utils/match.js';import{RenderFeatures as re,forwardRefWithAs as C,mergeProps as ae,useRender as L}from'../../utils/render.js';import{useDescriptions as je}from'../description/description.js';import{Keys as d}from'../keyboard.js';import{useLabelContext as qe,useLabels as se}from'../label/label.js';import{Portal as ze}from'../portal/portal.js';import{ActionTypes as r,ActivationTrigger as j,MenuState as m}from'./menu-machine.js';import{MenuContext as Ye,useMenuMachine as Ze,useMenuMachineContext as q}from'./menu-machine-glue.js';let et=ee;function tt(c,E){let p=B(),{__demoMode:a=!1,...s}=c,l=Ze({id:p,__demoMode:a}),[n,g,y]=D(l,T=>[T.menuState,T.itemsElement,T.buttonElement]),I=V(E),o=we.get(null),h=D(o,k(T=>o.selectors.isTop(T,p),[o,p]));Ie(h,[y,g],(T,u)=>{var f;l.send({type:r.CloseMenu}),Qe(u,Ve.Loose)||(T.preventDefault(),(f=l.state.buttonElement)==null||f.focus())});let _=P(()=>{l.send({type:r.CloseMenu})}),M=K(()=>({open:n===m.Open,close:_}),[n,_]),i={ref:I},b=L();return x.createElement(ve,null,x.createElement(Ye.Provider,{value:l},x.createElement(ke,{value:$e(n,{[m.Open]:X.Open,[m.Closed]:X.Closed})},b({ourProps:i,theirProps:s,slot:M,defaultTag:et,name:"Menu"}))))}let ot="button";function nt(c,E){let p=q("Menu.Button"),a=B(),{id:s=`headlessui-menu-button-${a}`,disabled:l=!1,autoFocus:n=!1,...g}=c,y=Q(null),I=Ge(),o=V(E,y,Ue(),P(e=>p.send({type:r.SetButtonElement,element:e}))),h=P(e=>{switch(e.key){case d.Space:case d.Enter:case d.ArrowDown:e.preventDefault(),e.stopPropagation(),p.send({type:r.OpenMenu,focus:{focus:A.First}});break;case d.ArrowUp:e.preventDefault(),e.stopPropagation(),p.send({type:r.OpenMenu,focus:{focus:A.Last}});break}}),_=P(e=>{switch(e.key){case d.Space:e.preventDefault();break}}),[M,i,b]=D(p,e=>[e.menuState,e.buttonElement,e.itemsElement]),T=M===m.Open;Se(T,{trigger:i,action:k(e=>{if(i!=null&&i.contains(e.target))return J.Ignore;let R=e.target.closest('[role="menuitem"]:not([data-disabled])');return Je.isHTMLElement(R)?J.Select(R):b!=null&&b.contains(e.target)?J.Ignore:J.Close},[i,b]),close:k(()=>p.send({type:r.CloseMenu}),[]),select:k(e=>e.click(),[])});let u=P(e=>{var R;if(e.button===0){if(Ke(e.currentTarget))return e.preventDefault();l||(M===m.Open?($(()=>p.send({type:r.CloseMenu})),(R=y.current)==null||R.focus({preventScroll:!0})):(e.preventDefault(),p.send({type:r.OpenMenu,focus:{focus:A.Nothing},trigger:j.Pointer})))}}),{isFocusVisible:f,focusProps:v}=Te({autoFocus:n}),{isHovered:S,hoverProps:O}=fe({isDisabled:l}),{pressed:F,pressProps:U}=Ee({disabled:l}),H=K(()=>({open:M===m.Open,active:F||M===m.Open,disabled:l,hover:S,focus:f,autofocus:n}),[M,S,f,F,l,n]),G=ae(I(),{ref:o,id:s,type:Re(c,y.current),"aria-haspopup":"menu","aria-controls":b==null?void 0:b.id,"aria-expanded":M===m.Open,disabled:l||void 0,autoFocus:n,onKeyDown:h,onKeyUp:_,onPointerDown:u},v,O,U);return L()({ourProps:G,theirProps:g,slot:H,defaultTag:ot,name:"Menu.Button"})}let rt="div",at=re.RenderStrategy|re.Static;function st(c,E){let p=B(),{id:a=`headlessui-menu-items-${p}`,anchor:s,portal:l=!1,modal:n=!0,transition:g=!1,...y}=c,I=Ne(s),o=q("Menu.Items"),[h,_]=Oe(I),M=He(),[i,b]=Pe(null),T=V(E,I?h:null,P(t=>o.send({type:r.SetItemsElement,element:t})),b),[u,f]=D(o,t=>[t.menuState,t.buttonElement]),v=te(f),S=te(i);I&&(l=!0);let O=Be(),[F,U]=Ce(g,i,O!==null?(O&X.Open)===X.Open:u===m.Open);_e(F,f,()=>{o.send({type:r.CloseMenu})});let H=D(o,t=>t.__demoMode),G=H?!1:n&&u===m.Open;De(G,S);let w=H?!1:n&&u===m.Open;Ae(w,{allowed:k(()=>[f,i],[f,i])});let e=u!==m.Open,le=ge(e,f)?!1:F;ye(()=>{let t=i;t&&u===m.Open&&t!==(S==null?void 0:S.activeElement)&&t.focus({preventScroll:!0})},[u,i,S]),Le(u===m.Open,{container:i,accept(t){return t.getAttribute("role")==="menuitem"?NodeFilter.FILTER_REJECT:t.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(t){t.setAttribute("role","none")}});let z=Me(),pe=P(t=>{var N,Y,Z;switch(z.dispose(),t.key){case d.Space:if(o.state.searchQuery!=="")return t.preventDefault(),t.stopPropagation(),o.send({type:r.Search,value:t.key});case d.Enter:if(t.preventDefault(),t.stopPropagation(),o.state.activeItemIndex!==null){let{dataRef:ce}=o.state.items[o.state.activeItemIndex];(Y=(N=ce.current)==null?void 0:N.domRef.current)==null||Y.click()}o.send({type:r.CloseMenu}),ne(o.state.buttonElement);break;case d.ArrowDown:return t.preventDefault(),t.stopPropagation(),o.send({type:r.GoToItem,focus:A.Next});case d.ArrowUp:return t.preventDefault(),t.stopPropagation(),o.send({type:r.GoToItem,focus:A.Previous});case d.Home:case d.PageUp:return t.preventDefault(),t.stopPropagation(),o.send({type:r.GoToItem,focus:A.First});case d.End:case d.PageDown:return t.preventDefault(),t.stopPropagation(),o.send({type:r.GoToItem,focus:A.Last});case d.Escape:t.preventDefault(),t.stopPropagation(),$(()=>o.send({type:r.CloseMenu})),(Z=o.state.buttonElement)==null||Z.focus({preventScroll:!0});break;case d.Tab:t.preventDefault(),t.stopPropagation(),$(()=>o.send({type:r.CloseMenu})),Xe(o.state.buttonElement,t.shiftKey?oe.Previous:oe.Next);break;default:t.key.length===1&&(o.send({type:r.Search,value:t.key}),z.setTimeout(()=>o.send({type:r.ClearSearch}),350));break}}),ie=P(t=>{switch(t.key){case d.Space:t.preventDefault();break}}),ue=K(()=>({open:u===m.Open}),[u]),de=ae(I?M():{},{"aria-activedescendant":D(o,o.selectors.activeDescendantId),"aria-labelledby":D(o,t=>{var N;return(N=t.buttonElement)==null?void 0:N.id}),id:a,onKeyDown:pe,onKeyUp:ie,role:"menu",tabIndex:u===m.Open?0:void 0,ref:T,style:{...y.style,..._,"--button-width":be(f,!0).width},...xe(U)}),me=L();return x.createElement(ze,{enabled:l?c.static||F:!1,ownerDocument:v},me({ourProps:de,theirProps:y,slot:ue,defaultTag:rt,features:at,visible:le,name:"Menu.Items"}))}let lt=ee;function pt(c,E){let p=B(),{id:a=`headlessui-menu-item-${p}`,disabled:s=!1,...l}=c,n=q("Menu.Item"),g=D(n,e=>n.selectors.isActive(e,a)),y=Q(null),I=V(E,y),o=D(n,e=>n.selectors.shouldScrollIntoView(e,a));W(()=>{if(o)return We().requestAnimationFrame(()=>{var e,R;(R=(e=y.current)==null?void 0:e.scrollIntoView)==null||R.call(e,{block:"nearest"})})},[o,y]);let h=Fe(y),_=Q({disabled:s,domRef:y,get textValue(){return h()}});W(()=>{_.current.disabled=s},[_,s]),W(()=>(n.actions.registerItem(a,_),()=>n.actions.unregisterItem(a)),[_,a]);let M=P(()=>{n.send({type:r.CloseMenu})}),i=P(e=>{if(s)return e.preventDefault();n.send({type:r.CloseMenu}),ne(n.state.buttonElement)}),b=P(()=>{if(s)return n.send({type:r.GoToItem,focus:A.Nothing});n.send({type:r.GoToItem,focus:A.Specific,id:a})}),T=he(),u=P(e=>{T.update(e),!s&&(g||n.send({type:r.GoToItem,focus:A.Specific,id:a,trigger:j.Pointer}))}),f=P(e=>{T.wasMoved(e)&&(s||g||n.send({type:r.GoToItem,focus:A.Specific,id:a,trigger:j.Pointer}))}),v=P(e=>{T.wasMoved(e)&&(s||g&&n.send({type:r.GoToItem,focus:A.Nothing}))}),[S,O]=se(),[F,U]=je(),H=K(()=>({active:g,focus:g,disabled:s,close:M}),[g,s,M]),G={id:a,ref:I,role:"menuitem",tabIndex:s===!0?void 0:-1,"aria-disabled":s===!0?!0:void 0,"aria-labelledby":S,"aria-describedby":F,disabled:void 0,onClick:i,onFocus:b,onPointerEnter:u,onMouseEnter:u,onPointerMove:f,onMouseMove:f,onPointerLeave:v,onMouseLeave:v},w=L();return x.createElement(O,null,x.createElement(U,null,w({ourProps:G,theirProps:l,slot:H,defaultTag:lt,name:"Menu.Item"})))}let it="div";function ut(c,E){let[p,a]=se(),s=c,l={ref:E,"aria-labelledby":p,role:"group"},n=L();return x.createElement(a,null,n({ourProps:l,theirProps:s,slot:{},defaultTag:it,name:"Menu.Section"}))}let dt="header";function mt(c,E){let p=B(),{id:a=`headlessui-menu-heading-${p}`,...s}=c,l=qe();W(()=>l.register(a),[a,l.register]);let n={id:a,ref:E,role:"presentation",...l.props};return L()({ourProps:n,theirProps:s,slot:{},defaultTag:dt,name:"Menu.Heading"})}let ct="div";function Tt(c,E){let p=c,a={ref:E,role:"separator"};return L()({ourProps:a,theirProps:p,slot:{},defaultTag:ct,name:"Menu.Separator"})}let ft=C(tt),yt=C(nt),Pt=C(st),Et=C(pt),gt=C(ut),Mt=C(mt),bt=C(Tt),lo=Object.assign(ft,{Button:yt,Items:Pt,Item:Et,Section:gt,Heading:Mt,Separator:bt});export{lo as Menu,yt as MenuButton,Mt as MenuHeading,Et as MenuItem,Pt as MenuItems,gt as MenuSection,bt as MenuSeparator};
