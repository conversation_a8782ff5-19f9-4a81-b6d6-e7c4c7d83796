'use strict';
// Code generated by protoc-gen-ts_proto. DO NOT EDIT.
// versions:
//   protoc-gen-ts_proto  v1.176.0
//   protoc               v3.19.1
// source: v1/search_get.proto
var __importDefault =
  (this && this.__importDefault) ||
  function (mod) {
    return mod && mod.__esModule ? mod : { default: mod };
  };
Object.defineProperty(exports, '__esModule', { value: true });
exports.RefPropertiesResult =
  exports.PropertiesResult =
  exports.MetadataResult =
  exports.SearchResult =
  exports.GroupByResult =
  exports.RerankReply =
  exports.SearchReply =
  exports.Rerank =
  exports.RefPropertiesRequest =
  exports.ObjectPropertiesRequest =
  exports.PropertiesRequest =
  exports.MetadataRequest =
  exports.SortBy =
  exports.GroupBy =
  exports.SearchRequest =
  exports.protobufPackage =
    void 0;
/* eslint-disable */
const long_1 = __importDefault(require('long'));
const minimal_js_1 = __importDefault(require('protobufjs/minimal.js'));
const struct_js_1 = require('../google/protobuf/struct.js');
const base_js_1 = require('./base.js');
const base_search_js_1 = require('./base_search.js');
const generative_js_1 = require('./generative.js');
const properties_js_1 = require('./properties.js');
exports.protobufPackage = 'weaviate.v1';
function createBaseSearchRequest() {
  return {
    collection: '',
    tenant: '',
    consistencyLevel: undefined,
    properties: undefined,
    metadata: undefined,
    groupBy: undefined,
    limit: 0,
    offset: 0,
    autocut: 0,
    after: '',
    sortBy: [],
    filters: undefined,
    hybridSearch: undefined,
    bm25Search: undefined,
    nearVector: undefined,
    nearObject: undefined,
    nearText: undefined,
    nearImage: undefined,
    nearAudio: undefined,
    nearVideo: undefined,
    nearDepth: undefined,
    nearThermal: undefined,
    nearImu: undefined,
    generative: undefined,
    rerank: undefined,
    uses123Api: false,
    uses125Api: false,
    uses127Api: false,
  };
}
exports.SearchRequest = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.collection !== '') {
      writer.uint32(10).string(message.collection);
    }
    if (message.tenant !== '') {
      writer.uint32(82).string(message.tenant);
    }
    if (message.consistencyLevel !== undefined) {
      writer.uint32(88).int32(message.consistencyLevel);
    }
    if (message.properties !== undefined) {
      exports.PropertiesRequest.encode(message.properties, writer.uint32(162).fork()).ldelim();
    }
    if (message.metadata !== undefined) {
      exports.MetadataRequest.encode(message.metadata, writer.uint32(170).fork()).ldelim();
    }
    if (message.groupBy !== undefined) {
      exports.GroupBy.encode(message.groupBy, writer.uint32(178).fork()).ldelim();
    }
    if (message.limit !== 0) {
      writer.uint32(240).uint32(message.limit);
    }
    if (message.offset !== 0) {
      writer.uint32(248).uint32(message.offset);
    }
    if (message.autocut !== 0) {
      writer.uint32(256).uint32(message.autocut);
    }
    if (message.after !== '') {
      writer.uint32(266).string(message.after);
    }
    for (const v of message.sortBy) {
      exports.SortBy.encode(v, writer.uint32(274).fork()).ldelim();
    }
    if (message.filters !== undefined) {
      base_js_1.Filters.encode(message.filters, writer.uint32(322).fork()).ldelim();
    }
    if (message.hybridSearch !== undefined) {
      base_search_js_1.Hybrid.encode(message.hybridSearch, writer.uint32(330).fork()).ldelim();
    }
    if (message.bm25Search !== undefined) {
      base_search_js_1.BM25.encode(message.bm25Search, writer.uint32(338).fork()).ldelim();
    }
    if (message.nearVector !== undefined) {
      base_search_js_1.NearVector.encode(message.nearVector, writer.uint32(346).fork()).ldelim();
    }
    if (message.nearObject !== undefined) {
      base_search_js_1.NearObject.encode(message.nearObject, writer.uint32(354).fork()).ldelim();
    }
    if (message.nearText !== undefined) {
      base_search_js_1.NearTextSearch.encode(message.nearText, writer.uint32(362).fork()).ldelim();
    }
    if (message.nearImage !== undefined) {
      base_search_js_1.NearImageSearch.encode(message.nearImage, writer.uint32(370).fork()).ldelim();
    }
    if (message.nearAudio !== undefined) {
      base_search_js_1.NearAudioSearch.encode(message.nearAudio, writer.uint32(378).fork()).ldelim();
    }
    if (message.nearVideo !== undefined) {
      base_search_js_1.NearVideoSearch.encode(message.nearVideo, writer.uint32(386).fork()).ldelim();
    }
    if (message.nearDepth !== undefined) {
      base_search_js_1.NearDepthSearch.encode(message.nearDepth, writer.uint32(394).fork()).ldelim();
    }
    if (message.nearThermal !== undefined) {
      base_search_js_1.NearThermalSearch.encode(message.nearThermal, writer.uint32(402).fork()).ldelim();
    }
    if (message.nearImu !== undefined) {
      base_search_js_1.NearIMUSearch.encode(message.nearImu, writer.uint32(410).fork()).ldelim();
    }
    if (message.generative !== undefined) {
      generative_js_1.GenerativeSearch.encode(message.generative, writer.uint32(482).fork()).ldelim();
    }
    if (message.rerank !== undefined) {
      exports.Rerank.encode(message.rerank, writer.uint32(490).fork()).ldelim();
    }
    if (message.uses123Api !== false) {
      writer.uint32(800).bool(message.uses123Api);
    }
    if (message.uses125Api !== false) {
      writer.uint32(808).bool(message.uses125Api);
    }
    if (message.uses127Api !== false) {
      writer.uint32(816).bool(message.uses127Api);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSearchRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.collection = reader.string();
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }
          message.tenant = reader.string();
          continue;
        case 11:
          if (tag !== 88) {
            break;
          }
          message.consistencyLevel = reader.int32();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }
          message.properties = exports.PropertiesRequest.decode(reader, reader.uint32());
          continue;
        case 21:
          if (tag !== 170) {
            break;
          }
          message.metadata = exports.MetadataRequest.decode(reader, reader.uint32());
          continue;
        case 22:
          if (tag !== 178) {
            break;
          }
          message.groupBy = exports.GroupBy.decode(reader, reader.uint32());
          continue;
        case 30:
          if (tag !== 240) {
            break;
          }
          message.limit = reader.uint32();
          continue;
        case 31:
          if (tag !== 248) {
            break;
          }
          message.offset = reader.uint32();
          continue;
        case 32:
          if (tag !== 256) {
            break;
          }
          message.autocut = reader.uint32();
          continue;
        case 33:
          if (tag !== 266) {
            break;
          }
          message.after = reader.string();
          continue;
        case 34:
          if (tag !== 274) {
            break;
          }
          message.sortBy.push(exports.SortBy.decode(reader, reader.uint32()));
          continue;
        case 40:
          if (tag !== 322) {
            break;
          }
          message.filters = base_js_1.Filters.decode(reader, reader.uint32());
          continue;
        case 41:
          if (tag !== 330) {
            break;
          }
          message.hybridSearch = base_search_js_1.Hybrid.decode(reader, reader.uint32());
          continue;
        case 42:
          if (tag !== 338) {
            break;
          }
          message.bm25Search = base_search_js_1.BM25.decode(reader, reader.uint32());
          continue;
        case 43:
          if (tag !== 346) {
            break;
          }
          message.nearVector = base_search_js_1.NearVector.decode(reader, reader.uint32());
          continue;
        case 44:
          if (tag !== 354) {
            break;
          }
          message.nearObject = base_search_js_1.NearObject.decode(reader, reader.uint32());
          continue;
        case 45:
          if (tag !== 362) {
            break;
          }
          message.nearText = base_search_js_1.NearTextSearch.decode(reader, reader.uint32());
          continue;
        case 46:
          if (tag !== 370) {
            break;
          }
          message.nearImage = base_search_js_1.NearImageSearch.decode(reader, reader.uint32());
          continue;
        case 47:
          if (tag !== 378) {
            break;
          }
          message.nearAudio = base_search_js_1.NearAudioSearch.decode(reader, reader.uint32());
          continue;
        case 48:
          if (tag !== 386) {
            break;
          }
          message.nearVideo = base_search_js_1.NearVideoSearch.decode(reader, reader.uint32());
          continue;
        case 49:
          if (tag !== 394) {
            break;
          }
          message.nearDepth = base_search_js_1.NearDepthSearch.decode(reader, reader.uint32());
          continue;
        case 50:
          if (tag !== 402) {
            break;
          }
          message.nearThermal = base_search_js_1.NearThermalSearch.decode(reader, reader.uint32());
          continue;
        case 51:
          if (tag !== 410) {
            break;
          }
          message.nearImu = base_search_js_1.NearIMUSearch.decode(reader, reader.uint32());
          continue;
        case 60:
          if (tag !== 482) {
            break;
          }
          message.generative = generative_js_1.GenerativeSearch.decode(reader, reader.uint32());
          continue;
        case 61:
          if (tag !== 490) {
            break;
          }
          message.rerank = exports.Rerank.decode(reader, reader.uint32());
          continue;
        case 100:
          if (tag !== 800) {
            break;
          }
          message.uses123Api = reader.bool();
          continue;
        case 101:
          if (tag !== 808) {
            break;
          }
          message.uses125Api = reader.bool();
          continue;
        case 102:
          if (tag !== 816) {
            break;
          }
          message.uses127Api = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      collection: isSet(object.collection) ? globalThis.String(object.collection) : '',
      tenant: isSet(object.tenant) ? globalThis.String(object.tenant) : '',
      consistencyLevel: isSet(object.consistencyLevel)
        ? (0, base_js_1.consistencyLevelFromJSON)(object.consistencyLevel)
        : undefined,
      properties: isSet(object.properties)
        ? exports.PropertiesRequest.fromJSON(object.properties)
        : undefined,
      metadata: isSet(object.metadata) ? exports.MetadataRequest.fromJSON(object.metadata) : undefined,
      groupBy: isSet(object.groupBy) ? exports.GroupBy.fromJSON(object.groupBy) : undefined,
      limit: isSet(object.limit) ? globalThis.Number(object.limit) : 0,
      offset: isSet(object.offset) ? globalThis.Number(object.offset) : 0,
      autocut: isSet(object.autocut) ? globalThis.Number(object.autocut) : 0,
      after: isSet(object.after) ? globalThis.String(object.after) : '',
      sortBy: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.sortBy)
        ? object.sortBy.map((e) => exports.SortBy.fromJSON(e))
        : [],
      filters: isSet(object.filters) ? base_js_1.Filters.fromJSON(object.filters) : undefined,
      hybridSearch: isSet(object.hybridSearch)
        ? base_search_js_1.Hybrid.fromJSON(object.hybridSearch)
        : undefined,
      bm25Search: isSet(object.bm25Search) ? base_search_js_1.BM25.fromJSON(object.bm25Search) : undefined,
      nearVector: isSet(object.nearVector)
        ? base_search_js_1.NearVector.fromJSON(object.nearVector)
        : undefined,
      nearObject: isSet(object.nearObject)
        ? base_search_js_1.NearObject.fromJSON(object.nearObject)
        : undefined,
      nearText: isSet(object.nearText)
        ? base_search_js_1.NearTextSearch.fromJSON(object.nearText)
        : undefined,
      nearImage: isSet(object.nearImage)
        ? base_search_js_1.NearImageSearch.fromJSON(object.nearImage)
        : undefined,
      nearAudio: isSet(object.nearAudio)
        ? base_search_js_1.NearAudioSearch.fromJSON(object.nearAudio)
        : undefined,
      nearVideo: isSet(object.nearVideo)
        ? base_search_js_1.NearVideoSearch.fromJSON(object.nearVideo)
        : undefined,
      nearDepth: isSet(object.nearDepth)
        ? base_search_js_1.NearDepthSearch.fromJSON(object.nearDepth)
        : undefined,
      nearThermal: isSet(object.nearThermal)
        ? base_search_js_1.NearThermalSearch.fromJSON(object.nearThermal)
        : undefined,
      nearImu: isSet(object.nearImu) ? base_search_js_1.NearIMUSearch.fromJSON(object.nearImu) : undefined,
      generative: isSet(object.generative)
        ? generative_js_1.GenerativeSearch.fromJSON(object.generative)
        : undefined,
      rerank: isSet(object.rerank) ? exports.Rerank.fromJSON(object.rerank) : undefined,
      uses123Api: isSet(object.uses123Api) ? globalThis.Boolean(object.uses123Api) : false,
      uses125Api: isSet(object.uses125Api) ? globalThis.Boolean(object.uses125Api) : false,
      uses127Api: isSet(object.uses127Api) ? globalThis.Boolean(object.uses127Api) : false,
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if (message.collection !== '') {
      obj.collection = message.collection;
    }
    if (message.tenant !== '') {
      obj.tenant = message.tenant;
    }
    if (message.consistencyLevel !== undefined) {
      obj.consistencyLevel = (0, base_js_1.consistencyLevelToJSON)(message.consistencyLevel);
    }
    if (message.properties !== undefined) {
      obj.properties = exports.PropertiesRequest.toJSON(message.properties);
    }
    if (message.metadata !== undefined) {
      obj.metadata = exports.MetadataRequest.toJSON(message.metadata);
    }
    if (message.groupBy !== undefined) {
      obj.groupBy = exports.GroupBy.toJSON(message.groupBy);
    }
    if (message.limit !== 0) {
      obj.limit = Math.round(message.limit);
    }
    if (message.offset !== 0) {
      obj.offset = Math.round(message.offset);
    }
    if (message.autocut !== 0) {
      obj.autocut = Math.round(message.autocut);
    }
    if (message.after !== '') {
      obj.after = message.after;
    }
    if ((_a = message.sortBy) === null || _a === void 0 ? void 0 : _a.length) {
      obj.sortBy = message.sortBy.map((e) => exports.SortBy.toJSON(e));
    }
    if (message.filters !== undefined) {
      obj.filters = base_js_1.Filters.toJSON(message.filters);
    }
    if (message.hybridSearch !== undefined) {
      obj.hybridSearch = base_search_js_1.Hybrid.toJSON(message.hybridSearch);
    }
    if (message.bm25Search !== undefined) {
      obj.bm25Search = base_search_js_1.BM25.toJSON(message.bm25Search);
    }
    if (message.nearVector !== undefined) {
      obj.nearVector = base_search_js_1.NearVector.toJSON(message.nearVector);
    }
    if (message.nearObject !== undefined) {
      obj.nearObject = base_search_js_1.NearObject.toJSON(message.nearObject);
    }
    if (message.nearText !== undefined) {
      obj.nearText = base_search_js_1.NearTextSearch.toJSON(message.nearText);
    }
    if (message.nearImage !== undefined) {
      obj.nearImage = base_search_js_1.NearImageSearch.toJSON(message.nearImage);
    }
    if (message.nearAudio !== undefined) {
      obj.nearAudio = base_search_js_1.NearAudioSearch.toJSON(message.nearAudio);
    }
    if (message.nearVideo !== undefined) {
      obj.nearVideo = base_search_js_1.NearVideoSearch.toJSON(message.nearVideo);
    }
    if (message.nearDepth !== undefined) {
      obj.nearDepth = base_search_js_1.NearDepthSearch.toJSON(message.nearDepth);
    }
    if (message.nearThermal !== undefined) {
      obj.nearThermal = base_search_js_1.NearThermalSearch.toJSON(message.nearThermal);
    }
    if (message.nearImu !== undefined) {
      obj.nearImu = base_search_js_1.NearIMUSearch.toJSON(message.nearImu);
    }
    if (message.generative !== undefined) {
      obj.generative = generative_js_1.GenerativeSearch.toJSON(message.generative);
    }
    if (message.rerank !== undefined) {
      obj.rerank = exports.Rerank.toJSON(message.rerank);
    }
    if (message.uses123Api !== false) {
      obj.uses123Api = message.uses123Api;
    }
    if (message.uses125Api !== false) {
      obj.uses125Api = message.uses125Api;
    }
    if (message.uses127Api !== false) {
      obj.uses127Api = message.uses127Api;
    }
    return obj;
  },
  create(base) {
    return exports.SearchRequest.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;
    const message = createBaseSearchRequest();
    message.collection = (_a = object.collection) !== null && _a !== void 0 ? _a : '';
    message.tenant = (_b = object.tenant) !== null && _b !== void 0 ? _b : '';
    message.consistencyLevel = (_c = object.consistencyLevel) !== null && _c !== void 0 ? _c : undefined;
    message.properties =
      object.properties !== undefined && object.properties !== null
        ? exports.PropertiesRequest.fromPartial(object.properties)
        : undefined;
    message.metadata =
      object.metadata !== undefined && object.metadata !== null
        ? exports.MetadataRequest.fromPartial(object.metadata)
        : undefined;
    message.groupBy =
      object.groupBy !== undefined && object.groupBy !== null
        ? exports.GroupBy.fromPartial(object.groupBy)
        : undefined;
    message.limit = (_d = object.limit) !== null && _d !== void 0 ? _d : 0;
    message.offset = (_e = object.offset) !== null && _e !== void 0 ? _e : 0;
    message.autocut = (_f = object.autocut) !== null && _f !== void 0 ? _f : 0;
    message.after = (_g = object.after) !== null && _g !== void 0 ? _g : '';
    message.sortBy =
      ((_h = object.sortBy) === null || _h === void 0
        ? void 0
        : _h.map((e) => exports.SortBy.fromPartial(e))) || [];
    message.filters =
      object.filters !== undefined && object.filters !== null
        ? base_js_1.Filters.fromPartial(object.filters)
        : undefined;
    message.hybridSearch =
      object.hybridSearch !== undefined && object.hybridSearch !== null
        ? base_search_js_1.Hybrid.fromPartial(object.hybridSearch)
        : undefined;
    message.bm25Search =
      object.bm25Search !== undefined && object.bm25Search !== null
        ? base_search_js_1.BM25.fromPartial(object.bm25Search)
        : undefined;
    message.nearVector =
      object.nearVector !== undefined && object.nearVector !== null
        ? base_search_js_1.NearVector.fromPartial(object.nearVector)
        : undefined;
    message.nearObject =
      object.nearObject !== undefined && object.nearObject !== null
        ? base_search_js_1.NearObject.fromPartial(object.nearObject)
        : undefined;
    message.nearText =
      object.nearText !== undefined && object.nearText !== null
        ? base_search_js_1.NearTextSearch.fromPartial(object.nearText)
        : undefined;
    message.nearImage =
      object.nearImage !== undefined && object.nearImage !== null
        ? base_search_js_1.NearImageSearch.fromPartial(object.nearImage)
        : undefined;
    message.nearAudio =
      object.nearAudio !== undefined && object.nearAudio !== null
        ? base_search_js_1.NearAudioSearch.fromPartial(object.nearAudio)
        : undefined;
    message.nearVideo =
      object.nearVideo !== undefined && object.nearVideo !== null
        ? base_search_js_1.NearVideoSearch.fromPartial(object.nearVideo)
        : undefined;
    message.nearDepth =
      object.nearDepth !== undefined && object.nearDepth !== null
        ? base_search_js_1.NearDepthSearch.fromPartial(object.nearDepth)
        : undefined;
    message.nearThermal =
      object.nearThermal !== undefined && object.nearThermal !== null
        ? base_search_js_1.NearThermalSearch.fromPartial(object.nearThermal)
        : undefined;
    message.nearImu =
      object.nearImu !== undefined && object.nearImu !== null
        ? base_search_js_1.NearIMUSearch.fromPartial(object.nearImu)
        : undefined;
    message.generative =
      object.generative !== undefined && object.generative !== null
        ? generative_js_1.GenerativeSearch.fromPartial(object.generative)
        : undefined;
    message.rerank =
      object.rerank !== undefined && object.rerank !== null
        ? exports.Rerank.fromPartial(object.rerank)
        : undefined;
    message.uses123Api = (_j = object.uses123Api) !== null && _j !== void 0 ? _j : false;
    message.uses125Api = (_k = object.uses125Api) !== null && _k !== void 0 ? _k : false;
    message.uses127Api = (_l = object.uses127Api) !== null && _l !== void 0 ? _l : false;
    return message;
  },
};
function createBaseGroupBy() {
  return { path: [], numberOfGroups: 0, objectsPerGroup: 0 };
}
exports.GroupBy = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    for (const v of message.path) {
      writer.uint32(10).string(v);
    }
    if (message.numberOfGroups !== 0) {
      writer.uint32(16).int32(message.numberOfGroups);
    }
    if (message.objectsPerGroup !== 0) {
      writer.uint32(24).int32(message.objectsPerGroup);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGroupBy();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.path.push(reader.string());
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.numberOfGroups = reader.int32();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }
          message.objectsPerGroup = reader.int32();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      path: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.path)
        ? object.path.map((e) => globalThis.String(e))
        : [],
      numberOfGroups: isSet(object.numberOfGroups) ? globalThis.Number(object.numberOfGroups) : 0,
      objectsPerGroup: isSet(object.objectsPerGroup) ? globalThis.Number(object.objectsPerGroup) : 0,
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.path) === null || _a === void 0 ? void 0 : _a.length) {
      obj.path = message.path;
    }
    if (message.numberOfGroups !== 0) {
      obj.numberOfGroups = Math.round(message.numberOfGroups);
    }
    if (message.objectsPerGroup !== 0) {
      obj.objectsPerGroup = Math.round(message.objectsPerGroup);
    }
    return obj;
  },
  create(base) {
    return exports.GroupBy.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c;
    const message = createBaseGroupBy();
    message.path = ((_a = object.path) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
    message.numberOfGroups = (_b = object.numberOfGroups) !== null && _b !== void 0 ? _b : 0;
    message.objectsPerGroup = (_c = object.objectsPerGroup) !== null && _c !== void 0 ? _c : 0;
    return message;
  },
};
function createBaseSortBy() {
  return { ascending: false, path: [] };
}
exports.SortBy = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.ascending !== false) {
      writer.uint32(8).bool(message.ascending);
    }
    for (const v of message.path) {
      writer.uint32(18).string(v);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSortBy();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.ascending = reader.bool();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.path.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      ascending: isSet(object.ascending) ? globalThis.Boolean(object.ascending) : false,
      path: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.path)
        ? object.path.map((e) => globalThis.String(e))
        : [],
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if (message.ascending !== false) {
      obj.ascending = message.ascending;
    }
    if ((_a = message.path) === null || _a === void 0 ? void 0 : _a.length) {
      obj.path = message.path;
    }
    return obj;
  },
  create(base) {
    return exports.SortBy.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseSortBy();
    message.ascending = (_a = object.ascending) !== null && _a !== void 0 ? _a : false;
    message.path = ((_b = object.path) === null || _b === void 0 ? void 0 : _b.map((e) => e)) || [];
    return message;
  },
};
function createBaseMetadataRequest() {
  return {
    uuid: false,
    vector: false,
    creationTimeUnix: false,
    lastUpdateTimeUnix: false,
    distance: false,
    certainty: false,
    score: false,
    explainScore: false,
    isConsistent: false,
    vectors: [],
  };
}
exports.MetadataRequest = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.uuid !== false) {
      writer.uint32(8).bool(message.uuid);
    }
    if (message.vector !== false) {
      writer.uint32(16).bool(message.vector);
    }
    if (message.creationTimeUnix !== false) {
      writer.uint32(24).bool(message.creationTimeUnix);
    }
    if (message.lastUpdateTimeUnix !== false) {
      writer.uint32(32).bool(message.lastUpdateTimeUnix);
    }
    if (message.distance !== false) {
      writer.uint32(40).bool(message.distance);
    }
    if (message.certainty !== false) {
      writer.uint32(48).bool(message.certainty);
    }
    if (message.score !== false) {
      writer.uint32(56).bool(message.score);
    }
    if (message.explainScore !== false) {
      writer.uint32(64).bool(message.explainScore);
    }
    if (message.isConsistent !== false) {
      writer.uint32(72).bool(message.isConsistent);
    }
    for (const v of message.vectors) {
      writer.uint32(82).string(v);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMetadataRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 8) {
            break;
          }
          message.uuid = reader.bool();
          continue;
        case 2:
          if (tag !== 16) {
            break;
          }
          message.vector = reader.bool();
          continue;
        case 3:
          if (tag !== 24) {
            break;
          }
          message.creationTimeUnix = reader.bool();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }
          message.lastUpdateTimeUnix = reader.bool();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }
          message.distance = reader.bool();
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }
          message.certainty = reader.bool();
          continue;
        case 7:
          if (tag !== 56) {
            break;
          }
          message.score = reader.bool();
          continue;
        case 8:
          if (tag !== 64) {
            break;
          }
          message.explainScore = reader.bool();
          continue;
        case 9:
          if (tag !== 72) {
            break;
          }
          message.isConsistent = reader.bool();
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }
          message.vectors.push(reader.string());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      uuid: isSet(object.uuid) ? globalThis.Boolean(object.uuid) : false,
      vector: isSet(object.vector) ? globalThis.Boolean(object.vector) : false,
      creationTimeUnix: isSet(object.creationTimeUnix) ? globalThis.Boolean(object.creationTimeUnix) : false,
      lastUpdateTimeUnix: isSet(object.lastUpdateTimeUnix)
        ? globalThis.Boolean(object.lastUpdateTimeUnix)
        : false,
      distance: isSet(object.distance) ? globalThis.Boolean(object.distance) : false,
      certainty: isSet(object.certainty) ? globalThis.Boolean(object.certainty) : false,
      score: isSet(object.score) ? globalThis.Boolean(object.score) : false,
      explainScore: isSet(object.explainScore) ? globalThis.Boolean(object.explainScore) : false,
      isConsistent: isSet(object.isConsistent) ? globalThis.Boolean(object.isConsistent) : false,
      vectors: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.vectors)
        ? object.vectors.map((e) => globalThis.String(e))
        : [],
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if (message.uuid !== false) {
      obj.uuid = message.uuid;
    }
    if (message.vector !== false) {
      obj.vector = message.vector;
    }
    if (message.creationTimeUnix !== false) {
      obj.creationTimeUnix = message.creationTimeUnix;
    }
    if (message.lastUpdateTimeUnix !== false) {
      obj.lastUpdateTimeUnix = message.lastUpdateTimeUnix;
    }
    if (message.distance !== false) {
      obj.distance = message.distance;
    }
    if (message.certainty !== false) {
      obj.certainty = message.certainty;
    }
    if (message.score !== false) {
      obj.score = message.score;
    }
    if (message.explainScore !== false) {
      obj.explainScore = message.explainScore;
    }
    if (message.isConsistent !== false) {
      obj.isConsistent = message.isConsistent;
    }
    if ((_a = message.vectors) === null || _a === void 0 ? void 0 : _a.length) {
      obj.vectors = message.vectors;
    }
    return obj;
  },
  create(base) {
    return exports.MetadataRequest.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
    const message = createBaseMetadataRequest();
    message.uuid = (_a = object.uuid) !== null && _a !== void 0 ? _a : false;
    message.vector = (_b = object.vector) !== null && _b !== void 0 ? _b : false;
    message.creationTimeUnix = (_c = object.creationTimeUnix) !== null && _c !== void 0 ? _c : false;
    message.lastUpdateTimeUnix = (_d = object.lastUpdateTimeUnix) !== null && _d !== void 0 ? _d : false;
    message.distance = (_e = object.distance) !== null && _e !== void 0 ? _e : false;
    message.certainty = (_f = object.certainty) !== null && _f !== void 0 ? _f : false;
    message.score = (_g = object.score) !== null && _g !== void 0 ? _g : false;
    message.explainScore = (_h = object.explainScore) !== null && _h !== void 0 ? _h : false;
    message.isConsistent = (_j = object.isConsistent) !== null && _j !== void 0 ? _j : false;
    message.vectors = ((_k = object.vectors) === null || _k === void 0 ? void 0 : _k.map((e) => e)) || [];
    return message;
  },
};
function createBasePropertiesRequest() {
  return { nonRefProperties: [], refProperties: [], objectProperties: [], returnAllNonrefProperties: false };
}
exports.PropertiesRequest = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    for (const v of message.nonRefProperties) {
      writer.uint32(10).string(v);
    }
    for (const v of message.refProperties) {
      exports.RefPropertiesRequest.encode(v, writer.uint32(18).fork()).ldelim();
    }
    for (const v of message.objectProperties) {
      exports.ObjectPropertiesRequest.encode(v, writer.uint32(26).fork()).ldelim();
    }
    if (message.returnAllNonrefProperties !== false) {
      writer.uint32(88).bool(message.returnAllNonrefProperties);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePropertiesRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.nonRefProperties.push(reader.string());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.refProperties.push(exports.RefPropertiesRequest.decode(reader, reader.uint32()));
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.objectProperties.push(exports.ObjectPropertiesRequest.decode(reader, reader.uint32()));
          continue;
        case 11:
          if (tag !== 88) {
            break;
          }
          message.returnAllNonrefProperties = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      nonRefProperties: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.nonRefProperties
      )
        ? object.nonRefProperties.map((e) => globalThis.String(e))
        : [],
      refProperties: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.refProperties
      )
        ? object.refProperties.map((e) => exports.RefPropertiesRequest.fromJSON(e))
        : [],
      objectProperties: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.objectProperties
      )
        ? object.objectProperties.map((e) => exports.ObjectPropertiesRequest.fromJSON(e))
        : [],
      returnAllNonrefProperties: isSet(object.returnAllNonrefProperties)
        ? globalThis.Boolean(object.returnAllNonrefProperties)
        : false,
    };
  },
  toJSON(message) {
    var _a, _b, _c;
    const obj = {};
    if ((_a = message.nonRefProperties) === null || _a === void 0 ? void 0 : _a.length) {
      obj.nonRefProperties = message.nonRefProperties;
    }
    if ((_b = message.refProperties) === null || _b === void 0 ? void 0 : _b.length) {
      obj.refProperties = message.refProperties.map((e) => exports.RefPropertiesRequest.toJSON(e));
    }
    if ((_c = message.objectProperties) === null || _c === void 0 ? void 0 : _c.length) {
      obj.objectProperties = message.objectProperties.map((e) => exports.ObjectPropertiesRequest.toJSON(e));
    }
    if (message.returnAllNonrefProperties !== false) {
      obj.returnAllNonrefProperties = message.returnAllNonrefProperties;
    }
    return obj;
  },
  create(base) {
    return exports.PropertiesRequest.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d;
    const message = createBasePropertiesRequest();
    message.nonRefProperties =
      ((_a = object.nonRefProperties) === null || _a === void 0 ? void 0 : _a.map((e) => e)) || [];
    message.refProperties =
      ((_b = object.refProperties) === null || _b === void 0
        ? void 0
        : _b.map((e) => exports.RefPropertiesRequest.fromPartial(e))) || [];
    message.objectProperties =
      ((_c = object.objectProperties) === null || _c === void 0
        ? void 0
        : _c.map((e) => exports.ObjectPropertiesRequest.fromPartial(e))) || [];
    message.returnAllNonrefProperties =
      (_d = object.returnAllNonrefProperties) !== null && _d !== void 0 ? _d : false;
    return message;
  },
};
function createBaseObjectPropertiesRequest() {
  return { propName: '', primitiveProperties: [], objectProperties: [] };
}
exports.ObjectPropertiesRequest = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.propName !== '') {
      writer.uint32(10).string(message.propName);
    }
    for (const v of message.primitiveProperties) {
      writer.uint32(18).string(v);
    }
    for (const v of message.objectProperties) {
      exports.ObjectPropertiesRequest.encode(v, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseObjectPropertiesRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.propName = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.primitiveProperties.push(reader.string());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.objectProperties.push(exports.ObjectPropertiesRequest.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      propName: isSet(object.propName) ? globalThis.String(object.propName) : '',
      primitiveProperties: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.primitiveProperties
      )
        ? object.primitiveProperties.map((e) => globalThis.String(e))
        : [],
      objectProperties: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.objectProperties
      )
        ? object.objectProperties.map((e) => exports.ObjectPropertiesRequest.fromJSON(e))
        : [],
    };
  },
  toJSON(message) {
    var _a, _b;
    const obj = {};
    if (message.propName !== '') {
      obj.propName = message.propName;
    }
    if ((_a = message.primitiveProperties) === null || _a === void 0 ? void 0 : _a.length) {
      obj.primitiveProperties = message.primitiveProperties;
    }
    if ((_b = message.objectProperties) === null || _b === void 0 ? void 0 : _b.length) {
      obj.objectProperties = message.objectProperties.map((e) => exports.ObjectPropertiesRequest.toJSON(e));
    }
    return obj;
  },
  create(base) {
    return exports.ObjectPropertiesRequest.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c;
    const message = createBaseObjectPropertiesRequest();
    message.propName = (_a = object.propName) !== null && _a !== void 0 ? _a : '';
    message.primitiveProperties =
      ((_b = object.primitiveProperties) === null || _b === void 0 ? void 0 : _b.map((e) => e)) || [];
    message.objectProperties =
      ((_c = object.objectProperties) === null || _c === void 0
        ? void 0
        : _c.map((e) => exports.ObjectPropertiesRequest.fromPartial(e))) || [];
    return message;
  },
};
function createBaseRefPropertiesRequest() {
  return { referenceProperty: '', properties: undefined, metadata: undefined, targetCollection: '' };
}
exports.RefPropertiesRequest = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.referenceProperty !== '') {
      writer.uint32(10).string(message.referenceProperty);
    }
    if (message.properties !== undefined) {
      exports.PropertiesRequest.encode(message.properties, writer.uint32(18).fork()).ldelim();
    }
    if (message.metadata !== undefined) {
      exports.MetadataRequest.encode(message.metadata, writer.uint32(26).fork()).ldelim();
    }
    if (message.targetCollection !== '') {
      writer.uint32(34).string(message.targetCollection);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRefPropertiesRequest();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.referenceProperty = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.properties = exports.PropertiesRequest.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.metadata = exports.MetadataRequest.decode(reader, reader.uint32());
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.targetCollection = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      referenceProperty: isSet(object.referenceProperty) ? globalThis.String(object.referenceProperty) : '',
      properties: isSet(object.properties)
        ? exports.PropertiesRequest.fromJSON(object.properties)
        : undefined,
      metadata: isSet(object.metadata) ? exports.MetadataRequest.fromJSON(object.metadata) : undefined,
      targetCollection: isSet(object.targetCollection) ? globalThis.String(object.targetCollection) : '',
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.referenceProperty !== '') {
      obj.referenceProperty = message.referenceProperty;
    }
    if (message.properties !== undefined) {
      obj.properties = exports.PropertiesRequest.toJSON(message.properties);
    }
    if (message.metadata !== undefined) {
      obj.metadata = exports.MetadataRequest.toJSON(message.metadata);
    }
    if (message.targetCollection !== '') {
      obj.targetCollection = message.targetCollection;
    }
    return obj;
  },
  create(base) {
    return exports.RefPropertiesRequest.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseRefPropertiesRequest();
    message.referenceProperty = (_a = object.referenceProperty) !== null && _a !== void 0 ? _a : '';
    message.properties =
      object.properties !== undefined && object.properties !== null
        ? exports.PropertiesRequest.fromPartial(object.properties)
        : undefined;
    message.metadata =
      object.metadata !== undefined && object.metadata !== null
        ? exports.MetadataRequest.fromPartial(object.metadata)
        : undefined;
    message.targetCollection = (_b = object.targetCollection) !== null && _b !== void 0 ? _b : '';
    return message;
  },
};
function createBaseRerank() {
  return { property: '', query: undefined };
}
exports.Rerank = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.property !== '') {
      writer.uint32(10).string(message.property);
    }
    if (message.query !== undefined) {
      writer.uint32(18).string(message.query);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRerank();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.property = reader.string();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.query = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      property: isSet(object.property) ? globalThis.String(object.property) : '',
      query: isSet(object.query) ? globalThis.String(object.query) : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.property !== '') {
      obj.property = message.property;
    }
    if (message.query !== undefined) {
      obj.query = message.query;
    }
    return obj;
  },
  create(base) {
    return exports.Rerank.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseRerank();
    message.property = (_a = object.property) !== null && _a !== void 0 ? _a : '';
    message.query = (_b = object.query) !== null && _b !== void 0 ? _b : undefined;
    return message;
  },
};
function createBaseSearchReply() {
  return {
    took: 0,
    results: [],
    generativeGroupedResult: undefined,
    groupByResults: [],
    generativeGroupedResults: undefined,
  };
}
exports.SearchReply = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.took !== 0) {
      writer.uint32(13).float(message.took);
    }
    for (const v of message.results) {
      exports.SearchResult.encode(v, writer.uint32(18).fork()).ldelim();
    }
    if (message.generativeGroupedResult !== undefined) {
      writer.uint32(26).string(message.generativeGroupedResult);
    }
    for (const v of message.groupByResults) {
      exports.GroupByResult.encode(v, writer.uint32(34).fork()).ldelim();
    }
    if (message.generativeGroupedResults !== undefined) {
      generative_js_1.GenerativeResult.encode(
        message.generativeGroupedResults,
        writer.uint32(42).fork()
      ).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSearchReply();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 13) {
            break;
          }
          message.took = reader.float();
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.results.push(exports.SearchResult.decode(reader, reader.uint32()));
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.generativeGroupedResult = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.groupByResults.push(exports.GroupByResult.decode(reader, reader.uint32()));
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }
          message.generativeGroupedResults = generative_js_1.GenerativeResult.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      took: isSet(object.took) ? globalThis.Number(object.took) : 0,
      results: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.results)
        ? object.results.map((e) => exports.SearchResult.fromJSON(e))
        : [],
      generativeGroupedResult: isSet(object.generativeGroupedResult)
        ? globalThis.String(object.generativeGroupedResult)
        : undefined,
      groupByResults: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.groupByResults
      )
        ? object.groupByResults.map((e) => exports.GroupByResult.fromJSON(e))
        : [],
      generativeGroupedResults: isSet(object.generativeGroupedResults)
        ? generative_js_1.GenerativeResult.fromJSON(object.generativeGroupedResults)
        : undefined,
    };
  },
  toJSON(message) {
    var _a, _b;
    const obj = {};
    if (message.took !== 0) {
      obj.took = message.took;
    }
    if ((_a = message.results) === null || _a === void 0 ? void 0 : _a.length) {
      obj.results = message.results.map((e) => exports.SearchResult.toJSON(e));
    }
    if (message.generativeGroupedResult !== undefined) {
      obj.generativeGroupedResult = message.generativeGroupedResult;
    }
    if ((_b = message.groupByResults) === null || _b === void 0 ? void 0 : _b.length) {
      obj.groupByResults = message.groupByResults.map((e) => exports.GroupByResult.toJSON(e));
    }
    if (message.generativeGroupedResults !== undefined) {
      obj.generativeGroupedResults = generative_js_1.GenerativeResult.toJSON(
        message.generativeGroupedResults
      );
    }
    return obj;
  },
  create(base) {
    return exports.SearchReply.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d;
    const message = createBaseSearchReply();
    message.took = (_a = object.took) !== null && _a !== void 0 ? _a : 0;
    message.results =
      ((_b = object.results) === null || _b === void 0
        ? void 0
        : _b.map((e) => exports.SearchResult.fromPartial(e))) || [];
    message.generativeGroupedResult =
      (_c = object.generativeGroupedResult) !== null && _c !== void 0 ? _c : undefined;
    message.groupByResults =
      ((_d = object.groupByResults) === null || _d === void 0
        ? void 0
        : _d.map((e) => exports.GroupByResult.fromPartial(e))) || [];
    message.generativeGroupedResults =
      object.generativeGroupedResults !== undefined && object.generativeGroupedResults !== null
        ? generative_js_1.GenerativeResult.fromPartial(object.generativeGroupedResults)
        : undefined;
    return message;
  },
};
function createBaseRerankReply() {
  return { score: 0 };
}
exports.RerankReply = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.score !== 0) {
      writer.uint32(9).double(message.score);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRerankReply();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 9) {
            break;
          }
          message.score = reader.double();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return { score: isSet(object.score) ? globalThis.Number(object.score) : 0 };
  },
  toJSON(message) {
    const obj = {};
    if (message.score !== 0) {
      obj.score = message.score;
    }
    return obj;
  },
  create(base) {
    return exports.RerankReply.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a;
    const message = createBaseRerankReply();
    message.score = (_a = object.score) !== null && _a !== void 0 ? _a : 0;
    return message;
  },
};
function createBaseGroupByResult() {
  return {
    name: '',
    minDistance: 0,
    maxDistance: 0,
    numberOfObjects: 0,
    objects: [],
    rerank: undefined,
    generative: undefined,
    generativeResult: undefined,
  };
}
exports.GroupByResult = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.name !== '') {
      writer.uint32(10).string(message.name);
    }
    if (message.minDistance !== 0) {
      writer.uint32(21).float(message.minDistance);
    }
    if (message.maxDistance !== 0) {
      writer.uint32(29).float(message.maxDistance);
    }
    if (message.numberOfObjects !== 0) {
      writer.uint32(32).int64(message.numberOfObjects);
    }
    for (const v of message.objects) {
      exports.SearchResult.encode(v, writer.uint32(42).fork()).ldelim();
    }
    if (message.rerank !== undefined) {
      exports.RerankReply.encode(message.rerank, writer.uint32(50).fork()).ldelim();
    }
    if (message.generative !== undefined) {
      generative_js_1.GenerativeReply.encode(message.generative, writer.uint32(58).fork()).ldelim();
    }
    if (message.generativeResult !== undefined) {
      generative_js_1.GenerativeResult.encode(message.generativeResult, writer.uint32(66).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseGroupByResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.name = reader.string();
          continue;
        case 2:
          if (tag !== 21) {
            break;
          }
          message.minDistance = reader.float();
          continue;
        case 3:
          if (tag !== 29) {
            break;
          }
          message.maxDistance = reader.float();
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }
          message.numberOfObjects = longToNumber(reader.int64());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }
          message.objects.push(exports.SearchResult.decode(reader, reader.uint32()));
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }
          message.rerank = exports.RerankReply.decode(reader, reader.uint32());
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }
          message.generative = generative_js_1.GenerativeReply.decode(reader, reader.uint32());
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }
          message.generativeResult = generative_js_1.GenerativeResult.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      name: isSet(object.name) ? globalThis.String(object.name) : '',
      minDistance: isSet(object.minDistance) ? globalThis.Number(object.minDistance) : 0,
      maxDistance: isSet(object.maxDistance) ? globalThis.Number(object.maxDistance) : 0,
      numberOfObjects: isSet(object.numberOfObjects) ? globalThis.Number(object.numberOfObjects) : 0,
      objects: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.objects)
        ? object.objects.map((e) => exports.SearchResult.fromJSON(e))
        : [],
      rerank: isSet(object.rerank) ? exports.RerankReply.fromJSON(object.rerank) : undefined,
      generative: isSet(object.generative)
        ? generative_js_1.GenerativeReply.fromJSON(object.generative)
        : undefined,
      generativeResult: isSet(object.generativeResult)
        ? generative_js_1.GenerativeResult.fromJSON(object.generativeResult)
        : undefined,
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if (message.name !== '') {
      obj.name = message.name;
    }
    if (message.minDistance !== 0) {
      obj.minDistance = message.minDistance;
    }
    if (message.maxDistance !== 0) {
      obj.maxDistance = message.maxDistance;
    }
    if (message.numberOfObjects !== 0) {
      obj.numberOfObjects = Math.round(message.numberOfObjects);
    }
    if ((_a = message.objects) === null || _a === void 0 ? void 0 : _a.length) {
      obj.objects = message.objects.map((e) => exports.SearchResult.toJSON(e));
    }
    if (message.rerank !== undefined) {
      obj.rerank = exports.RerankReply.toJSON(message.rerank);
    }
    if (message.generative !== undefined) {
      obj.generative = generative_js_1.GenerativeReply.toJSON(message.generative);
    }
    if (message.generativeResult !== undefined) {
      obj.generativeResult = generative_js_1.GenerativeResult.toJSON(message.generativeResult);
    }
    return obj;
  },
  create(base) {
    return exports.GroupByResult.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e;
    const message = createBaseGroupByResult();
    message.name = (_a = object.name) !== null && _a !== void 0 ? _a : '';
    message.minDistance = (_b = object.minDistance) !== null && _b !== void 0 ? _b : 0;
    message.maxDistance = (_c = object.maxDistance) !== null && _c !== void 0 ? _c : 0;
    message.numberOfObjects = (_d = object.numberOfObjects) !== null && _d !== void 0 ? _d : 0;
    message.objects =
      ((_e = object.objects) === null || _e === void 0
        ? void 0
        : _e.map((e) => exports.SearchResult.fromPartial(e))) || [];
    message.rerank =
      object.rerank !== undefined && object.rerank !== null
        ? exports.RerankReply.fromPartial(object.rerank)
        : undefined;
    message.generative =
      object.generative !== undefined && object.generative !== null
        ? generative_js_1.GenerativeReply.fromPartial(object.generative)
        : undefined;
    message.generativeResult =
      object.generativeResult !== undefined && object.generativeResult !== null
        ? generative_js_1.GenerativeResult.fromPartial(object.generativeResult)
        : undefined;
    return message;
  },
};
function createBaseSearchResult() {
  return { properties: undefined, metadata: undefined, generative: undefined };
}
exports.SearchResult = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.properties !== undefined) {
      exports.PropertiesResult.encode(message.properties, writer.uint32(10).fork()).ldelim();
    }
    if (message.metadata !== undefined) {
      exports.MetadataResult.encode(message.metadata, writer.uint32(18).fork()).ldelim();
    }
    if (message.generative !== undefined) {
      generative_js_1.GenerativeResult.encode(message.generative, writer.uint32(26).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseSearchResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.properties = exports.PropertiesResult.decode(reader, reader.uint32());
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.metadata = exports.MetadataResult.decode(reader, reader.uint32());
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.generative = generative_js_1.GenerativeResult.decode(reader, reader.uint32());
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      properties: isSet(object.properties) ? exports.PropertiesResult.fromJSON(object.properties) : undefined,
      metadata: isSet(object.metadata) ? exports.MetadataResult.fromJSON(object.metadata) : undefined,
      generative: isSet(object.generative)
        ? generative_js_1.GenerativeResult.fromJSON(object.generative)
        : undefined,
    };
  },
  toJSON(message) {
    const obj = {};
    if (message.properties !== undefined) {
      obj.properties = exports.PropertiesResult.toJSON(message.properties);
    }
    if (message.metadata !== undefined) {
      obj.metadata = exports.MetadataResult.toJSON(message.metadata);
    }
    if (message.generative !== undefined) {
      obj.generative = generative_js_1.GenerativeResult.toJSON(message.generative);
    }
    return obj;
  },
  create(base) {
    return exports.SearchResult.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    const message = createBaseSearchResult();
    message.properties =
      object.properties !== undefined && object.properties !== null
        ? exports.PropertiesResult.fromPartial(object.properties)
        : undefined;
    message.metadata =
      object.metadata !== undefined && object.metadata !== null
        ? exports.MetadataResult.fromPartial(object.metadata)
        : undefined;
    message.generative =
      object.generative !== undefined && object.generative !== null
        ? generative_js_1.GenerativeResult.fromPartial(object.generative)
        : undefined;
    return message;
  },
};
function createBaseMetadataResult() {
  return {
    id: '',
    vector: [],
    creationTimeUnix: 0,
    creationTimeUnixPresent: false,
    lastUpdateTimeUnix: 0,
    lastUpdateTimeUnixPresent: false,
    distance: 0,
    distancePresent: false,
    certainty: 0,
    certaintyPresent: false,
    score: 0,
    scorePresent: false,
    explainScore: '',
    explainScorePresent: false,
    isConsistent: undefined,
    generative: '',
    generativePresent: false,
    isConsistentPresent: false,
    vectorBytes: new Uint8Array(0),
    idAsBytes: new Uint8Array(0),
    rerankScore: 0,
    rerankScorePresent: false,
    vectors: [],
  };
}
exports.MetadataResult = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.id !== '') {
      writer.uint32(10).string(message.id);
    }
    writer.uint32(18).fork();
    for (const v of message.vector) {
      writer.float(v);
    }
    writer.ldelim();
    if (message.creationTimeUnix !== 0) {
      writer.uint32(24).int64(message.creationTimeUnix);
    }
    if (message.creationTimeUnixPresent !== false) {
      writer.uint32(32).bool(message.creationTimeUnixPresent);
    }
    if (message.lastUpdateTimeUnix !== 0) {
      writer.uint32(40).int64(message.lastUpdateTimeUnix);
    }
    if (message.lastUpdateTimeUnixPresent !== false) {
      writer.uint32(48).bool(message.lastUpdateTimeUnixPresent);
    }
    if (message.distance !== 0) {
      writer.uint32(61).float(message.distance);
    }
    if (message.distancePresent !== false) {
      writer.uint32(64).bool(message.distancePresent);
    }
    if (message.certainty !== 0) {
      writer.uint32(77).float(message.certainty);
    }
    if (message.certaintyPresent !== false) {
      writer.uint32(80).bool(message.certaintyPresent);
    }
    if (message.score !== 0) {
      writer.uint32(93).float(message.score);
    }
    if (message.scorePresent !== false) {
      writer.uint32(96).bool(message.scorePresent);
    }
    if (message.explainScore !== '') {
      writer.uint32(106).string(message.explainScore);
    }
    if (message.explainScorePresent !== false) {
      writer.uint32(112).bool(message.explainScorePresent);
    }
    if (message.isConsistent !== undefined) {
      writer.uint32(120).bool(message.isConsistent);
    }
    if (message.generative !== '') {
      writer.uint32(130).string(message.generative);
    }
    if (message.generativePresent !== false) {
      writer.uint32(136).bool(message.generativePresent);
    }
    if (message.isConsistentPresent !== false) {
      writer.uint32(144).bool(message.isConsistentPresent);
    }
    if (message.vectorBytes.length !== 0) {
      writer.uint32(154).bytes(message.vectorBytes);
    }
    if (message.idAsBytes.length !== 0) {
      writer.uint32(162).bytes(message.idAsBytes);
    }
    if (message.rerankScore !== 0) {
      writer.uint32(169).double(message.rerankScore);
    }
    if (message.rerankScorePresent !== false) {
      writer.uint32(176).bool(message.rerankScorePresent);
    }
    for (const v of message.vectors) {
      base_js_1.Vectors.encode(v, writer.uint32(186).fork()).ldelim();
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseMetadataResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.id = reader.string();
          continue;
        case 2:
          if (tag === 21) {
            message.vector.push(reader.float());
            continue;
          }
          if (tag === 18) {
            const end2 = reader.uint32() + reader.pos;
            while (reader.pos < end2) {
              message.vector.push(reader.float());
            }
            continue;
          }
          break;
        case 3:
          if (tag !== 24) {
            break;
          }
          message.creationTimeUnix = longToNumber(reader.int64());
          continue;
        case 4:
          if (tag !== 32) {
            break;
          }
          message.creationTimeUnixPresent = reader.bool();
          continue;
        case 5:
          if (tag !== 40) {
            break;
          }
          message.lastUpdateTimeUnix = longToNumber(reader.int64());
          continue;
        case 6:
          if (tag !== 48) {
            break;
          }
          message.lastUpdateTimeUnixPresent = reader.bool();
          continue;
        case 7:
          if (tag !== 61) {
            break;
          }
          message.distance = reader.float();
          continue;
        case 8:
          if (tag !== 64) {
            break;
          }
          message.distancePresent = reader.bool();
          continue;
        case 9:
          if (tag !== 77) {
            break;
          }
          message.certainty = reader.float();
          continue;
        case 10:
          if (tag !== 80) {
            break;
          }
          message.certaintyPresent = reader.bool();
          continue;
        case 11:
          if (tag !== 93) {
            break;
          }
          message.score = reader.float();
          continue;
        case 12:
          if (tag !== 96) {
            break;
          }
          message.scorePresent = reader.bool();
          continue;
        case 13:
          if (tag !== 106) {
            break;
          }
          message.explainScore = reader.string();
          continue;
        case 14:
          if (tag !== 112) {
            break;
          }
          message.explainScorePresent = reader.bool();
          continue;
        case 15:
          if (tag !== 120) {
            break;
          }
          message.isConsistent = reader.bool();
          continue;
        case 16:
          if (tag !== 130) {
            break;
          }
          message.generative = reader.string();
          continue;
        case 17:
          if (tag !== 136) {
            break;
          }
          message.generativePresent = reader.bool();
          continue;
        case 18:
          if (tag !== 144) {
            break;
          }
          message.isConsistentPresent = reader.bool();
          continue;
        case 19:
          if (tag !== 154) {
            break;
          }
          message.vectorBytes = reader.bytes();
          continue;
        case 20:
          if (tag !== 162) {
            break;
          }
          message.idAsBytes = reader.bytes();
          continue;
        case 21:
          if (tag !== 169) {
            break;
          }
          message.rerankScore = reader.double();
          continue;
        case 22:
          if (tag !== 176) {
            break;
          }
          message.rerankScorePresent = reader.bool();
          continue;
        case 23:
          if (tag !== 186) {
            break;
          }
          message.vectors.push(base_js_1.Vectors.decode(reader, reader.uint32()));
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      id: isSet(object.id) ? globalThis.String(object.id) : '',
      vector: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.vector)
        ? object.vector.map((e) => globalThis.Number(e))
        : [],
      creationTimeUnix: isSet(object.creationTimeUnix) ? globalThis.Number(object.creationTimeUnix) : 0,
      creationTimeUnixPresent: isSet(object.creationTimeUnixPresent)
        ? globalThis.Boolean(object.creationTimeUnixPresent)
        : false,
      lastUpdateTimeUnix: isSet(object.lastUpdateTimeUnix) ? globalThis.Number(object.lastUpdateTimeUnix) : 0,
      lastUpdateTimeUnixPresent: isSet(object.lastUpdateTimeUnixPresent)
        ? globalThis.Boolean(object.lastUpdateTimeUnixPresent)
        : false,
      distance: isSet(object.distance) ? globalThis.Number(object.distance) : 0,
      distancePresent: isSet(object.distancePresent) ? globalThis.Boolean(object.distancePresent) : false,
      certainty: isSet(object.certainty) ? globalThis.Number(object.certainty) : 0,
      certaintyPresent: isSet(object.certaintyPresent) ? globalThis.Boolean(object.certaintyPresent) : false,
      score: isSet(object.score) ? globalThis.Number(object.score) : 0,
      scorePresent: isSet(object.scorePresent) ? globalThis.Boolean(object.scorePresent) : false,
      explainScore: isSet(object.explainScore) ? globalThis.String(object.explainScore) : '',
      explainScorePresent: isSet(object.explainScorePresent)
        ? globalThis.Boolean(object.explainScorePresent)
        : false,
      isConsistent: isSet(object.isConsistent) ? globalThis.Boolean(object.isConsistent) : undefined,
      generative: isSet(object.generative) ? globalThis.String(object.generative) : '',
      generativePresent: isSet(object.generativePresent)
        ? globalThis.Boolean(object.generativePresent)
        : false,
      isConsistentPresent: isSet(object.isConsistentPresent)
        ? globalThis.Boolean(object.isConsistentPresent)
        : false,
      vectorBytes: isSet(object.vectorBytes) ? bytesFromBase64(object.vectorBytes) : new Uint8Array(0),
      idAsBytes: isSet(object.idAsBytes) ? bytesFromBase64(object.idAsBytes) : new Uint8Array(0),
      rerankScore: isSet(object.rerankScore) ? globalThis.Number(object.rerankScore) : 0,
      rerankScorePresent: isSet(object.rerankScorePresent)
        ? globalThis.Boolean(object.rerankScorePresent)
        : false,
      vectors: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.vectors)
        ? object.vectors.map((e) => base_js_1.Vectors.fromJSON(e))
        : [],
    };
  },
  toJSON(message) {
    var _a, _b;
    const obj = {};
    if (message.id !== '') {
      obj.id = message.id;
    }
    if ((_a = message.vector) === null || _a === void 0 ? void 0 : _a.length) {
      obj.vector = message.vector;
    }
    if (message.creationTimeUnix !== 0) {
      obj.creationTimeUnix = Math.round(message.creationTimeUnix);
    }
    if (message.creationTimeUnixPresent !== false) {
      obj.creationTimeUnixPresent = message.creationTimeUnixPresent;
    }
    if (message.lastUpdateTimeUnix !== 0) {
      obj.lastUpdateTimeUnix = Math.round(message.lastUpdateTimeUnix);
    }
    if (message.lastUpdateTimeUnixPresent !== false) {
      obj.lastUpdateTimeUnixPresent = message.lastUpdateTimeUnixPresent;
    }
    if (message.distance !== 0) {
      obj.distance = message.distance;
    }
    if (message.distancePresent !== false) {
      obj.distancePresent = message.distancePresent;
    }
    if (message.certainty !== 0) {
      obj.certainty = message.certainty;
    }
    if (message.certaintyPresent !== false) {
      obj.certaintyPresent = message.certaintyPresent;
    }
    if (message.score !== 0) {
      obj.score = message.score;
    }
    if (message.scorePresent !== false) {
      obj.scorePresent = message.scorePresent;
    }
    if (message.explainScore !== '') {
      obj.explainScore = message.explainScore;
    }
    if (message.explainScorePresent !== false) {
      obj.explainScorePresent = message.explainScorePresent;
    }
    if (message.isConsistent !== undefined) {
      obj.isConsistent = message.isConsistent;
    }
    if (message.generative !== '') {
      obj.generative = message.generative;
    }
    if (message.generativePresent !== false) {
      obj.generativePresent = message.generativePresent;
    }
    if (message.isConsistentPresent !== false) {
      obj.isConsistentPresent = message.isConsistentPresent;
    }
    if (message.vectorBytes.length !== 0) {
      obj.vectorBytes = base64FromBytes(message.vectorBytes);
    }
    if (message.idAsBytes.length !== 0) {
      obj.idAsBytes = base64FromBytes(message.idAsBytes);
    }
    if (message.rerankScore !== 0) {
      obj.rerankScore = message.rerankScore;
    }
    if (message.rerankScorePresent !== false) {
      obj.rerankScorePresent = message.rerankScorePresent;
    }
    if ((_b = message.vectors) === null || _b === void 0 ? void 0 : _b.length) {
      obj.vectors = message.vectors.map((e) => base_js_1.Vectors.toJSON(e));
    }
    return obj;
  },
  create(base) {
    return exports.MetadataResult.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l, _m, _o, _p, _q, _r, _s, _t, _u, _v, _w, _x, _y;
    const message = createBaseMetadataResult();
    message.id = (_a = object.id) !== null && _a !== void 0 ? _a : '';
    message.vector = ((_b = object.vector) === null || _b === void 0 ? void 0 : _b.map((e) => e)) || [];
    message.creationTimeUnix = (_c = object.creationTimeUnix) !== null && _c !== void 0 ? _c : 0;
    message.creationTimeUnixPresent =
      (_d = object.creationTimeUnixPresent) !== null && _d !== void 0 ? _d : false;
    message.lastUpdateTimeUnix = (_e = object.lastUpdateTimeUnix) !== null && _e !== void 0 ? _e : 0;
    message.lastUpdateTimeUnixPresent =
      (_f = object.lastUpdateTimeUnixPresent) !== null && _f !== void 0 ? _f : false;
    message.distance = (_g = object.distance) !== null && _g !== void 0 ? _g : 0;
    message.distancePresent = (_h = object.distancePresent) !== null && _h !== void 0 ? _h : false;
    message.certainty = (_j = object.certainty) !== null && _j !== void 0 ? _j : 0;
    message.certaintyPresent = (_k = object.certaintyPresent) !== null && _k !== void 0 ? _k : false;
    message.score = (_l = object.score) !== null && _l !== void 0 ? _l : 0;
    message.scorePresent = (_m = object.scorePresent) !== null && _m !== void 0 ? _m : false;
    message.explainScore = (_o = object.explainScore) !== null && _o !== void 0 ? _o : '';
    message.explainScorePresent = (_p = object.explainScorePresent) !== null && _p !== void 0 ? _p : false;
    message.isConsistent = (_q = object.isConsistent) !== null && _q !== void 0 ? _q : undefined;
    message.generative = (_r = object.generative) !== null && _r !== void 0 ? _r : '';
    message.generativePresent = (_s = object.generativePresent) !== null && _s !== void 0 ? _s : false;
    message.isConsistentPresent = (_t = object.isConsistentPresent) !== null && _t !== void 0 ? _t : false;
    message.vectorBytes = (_u = object.vectorBytes) !== null && _u !== void 0 ? _u : new Uint8Array(0);
    message.idAsBytes = (_v = object.idAsBytes) !== null && _v !== void 0 ? _v : new Uint8Array(0);
    message.rerankScore = (_w = object.rerankScore) !== null && _w !== void 0 ? _w : 0;
    message.rerankScorePresent = (_x = object.rerankScorePresent) !== null && _x !== void 0 ? _x : false;
    message.vectors =
      ((_y = object.vectors) === null || _y === void 0
        ? void 0
        : _y.map((e) => base_js_1.Vectors.fromPartial(e))) || [];
    return message;
  },
};
function createBasePropertiesResult() {
  return {
    nonRefProperties: undefined,
    refProps: [],
    targetCollection: '',
    metadata: undefined,
    numberArrayProperties: [],
    intArrayProperties: [],
    textArrayProperties: [],
    booleanArrayProperties: [],
    objectProperties: [],
    objectArrayProperties: [],
    nonRefProps: undefined,
    refPropsRequested: false,
  };
}
exports.PropertiesResult = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    if (message.nonRefProperties !== undefined) {
      struct_js_1.Struct.encode(
        struct_js_1.Struct.wrap(message.nonRefProperties),
        writer.uint32(10).fork()
      ).ldelim();
    }
    for (const v of message.refProps) {
      exports.RefPropertiesResult.encode(v, writer.uint32(18).fork()).ldelim();
    }
    if (message.targetCollection !== '') {
      writer.uint32(26).string(message.targetCollection);
    }
    if (message.metadata !== undefined) {
      exports.MetadataResult.encode(message.metadata, writer.uint32(34).fork()).ldelim();
    }
    for (const v of message.numberArrayProperties) {
      base_js_1.NumberArrayProperties.encode(v, writer.uint32(42).fork()).ldelim();
    }
    for (const v of message.intArrayProperties) {
      base_js_1.IntArrayProperties.encode(v, writer.uint32(50).fork()).ldelim();
    }
    for (const v of message.textArrayProperties) {
      base_js_1.TextArrayProperties.encode(v, writer.uint32(58).fork()).ldelim();
    }
    for (const v of message.booleanArrayProperties) {
      base_js_1.BooleanArrayProperties.encode(v, writer.uint32(66).fork()).ldelim();
    }
    for (const v of message.objectProperties) {
      base_js_1.ObjectProperties.encode(v, writer.uint32(74).fork()).ldelim();
    }
    for (const v of message.objectArrayProperties) {
      base_js_1.ObjectArrayProperties.encode(v, writer.uint32(82).fork()).ldelim();
    }
    if (message.nonRefProps !== undefined) {
      properties_js_1.Properties.encode(message.nonRefProps, writer.uint32(90).fork()).ldelim();
    }
    if (message.refPropsRequested !== false) {
      writer.uint32(96).bool(message.refPropsRequested);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBasePropertiesResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.nonRefProperties = struct_js_1.Struct.unwrap(
            struct_js_1.Struct.decode(reader, reader.uint32())
          );
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.refProps.push(exports.RefPropertiesResult.decode(reader, reader.uint32()));
          continue;
        case 3:
          if (tag !== 26) {
            break;
          }
          message.targetCollection = reader.string();
          continue;
        case 4:
          if (tag !== 34) {
            break;
          }
          message.metadata = exports.MetadataResult.decode(reader, reader.uint32());
          continue;
        case 5:
          if (tag !== 42) {
            break;
          }
          message.numberArrayProperties.push(base_js_1.NumberArrayProperties.decode(reader, reader.uint32()));
          continue;
        case 6:
          if (tag !== 50) {
            break;
          }
          message.intArrayProperties.push(base_js_1.IntArrayProperties.decode(reader, reader.uint32()));
          continue;
        case 7:
          if (tag !== 58) {
            break;
          }
          message.textArrayProperties.push(base_js_1.TextArrayProperties.decode(reader, reader.uint32()));
          continue;
        case 8:
          if (tag !== 66) {
            break;
          }
          message.booleanArrayProperties.push(
            base_js_1.BooleanArrayProperties.decode(reader, reader.uint32())
          );
          continue;
        case 9:
          if (tag !== 74) {
            break;
          }
          message.objectProperties.push(base_js_1.ObjectProperties.decode(reader, reader.uint32()));
          continue;
        case 10:
          if (tag !== 82) {
            break;
          }
          message.objectArrayProperties.push(base_js_1.ObjectArrayProperties.decode(reader, reader.uint32()));
          continue;
        case 11:
          if (tag !== 90) {
            break;
          }
          message.nonRefProps = properties_js_1.Properties.decode(reader, reader.uint32());
          continue;
        case 12:
          if (tag !== 96) {
            break;
          }
          message.refPropsRequested = reader.bool();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      nonRefProperties: isObject(object.nonRefProperties) ? object.nonRefProperties : undefined,
      refProps: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.refProps)
        ? object.refProps.map((e) => exports.RefPropertiesResult.fromJSON(e))
        : [],
      targetCollection: isSet(object.targetCollection) ? globalThis.String(object.targetCollection) : '',
      metadata: isSet(object.metadata) ? exports.MetadataResult.fromJSON(object.metadata) : undefined,
      numberArrayProperties: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.numberArrayProperties
      )
        ? object.numberArrayProperties.map((e) => base_js_1.NumberArrayProperties.fromJSON(e))
        : [],
      intArrayProperties: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.intArrayProperties
      )
        ? object.intArrayProperties.map((e) => base_js_1.IntArrayProperties.fromJSON(e))
        : [],
      textArrayProperties: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.textArrayProperties
      )
        ? object.textArrayProperties.map((e) => base_js_1.TextArrayProperties.fromJSON(e))
        : [],
      booleanArrayProperties: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.booleanArrayProperties
      )
        ? object.booleanArrayProperties.map((e) => base_js_1.BooleanArrayProperties.fromJSON(e))
        : [],
      objectProperties: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.objectProperties
      )
        ? object.objectProperties.map((e) => base_js_1.ObjectProperties.fromJSON(e))
        : [],
      objectArrayProperties: globalThis.Array.isArray(
        object === null || object === void 0 ? void 0 : object.objectArrayProperties
      )
        ? object.objectArrayProperties.map((e) => base_js_1.ObjectArrayProperties.fromJSON(e))
        : [],
      nonRefProps: isSet(object.nonRefProps)
        ? properties_js_1.Properties.fromJSON(object.nonRefProps)
        : undefined,
      refPropsRequested: isSet(object.refPropsRequested)
        ? globalThis.Boolean(object.refPropsRequested)
        : false,
    };
  },
  toJSON(message) {
    var _a, _b, _c, _d, _e, _f, _g;
    const obj = {};
    if (message.nonRefProperties !== undefined) {
      obj.nonRefProperties = message.nonRefProperties;
    }
    if ((_a = message.refProps) === null || _a === void 0 ? void 0 : _a.length) {
      obj.refProps = message.refProps.map((e) => exports.RefPropertiesResult.toJSON(e));
    }
    if (message.targetCollection !== '') {
      obj.targetCollection = message.targetCollection;
    }
    if (message.metadata !== undefined) {
      obj.metadata = exports.MetadataResult.toJSON(message.metadata);
    }
    if ((_b = message.numberArrayProperties) === null || _b === void 0 ? void 0 : _b.length) {
      obj.numberArrayProperties = message.numberArrayProperties.map((e) =>
        base_js_1.NumberArrayProperties.toJSON(e)
      );
    }
    if ((_c = message.intArrayProperties) === null || _c === void 0 ? void 0 : _c.length) {
      obj.intArrayProperties = message.intArrayProperties.map((e) => base_js_1.IntArrayProperties.toJSON(e));
    }
    if ((_d = message.textArrayProperties) === null || _d === void 0 ? void 0 : _d.length) {
      obj.textArrayProperties = message.textArrayProperties.map((e) =>
        base_js_1.TextArrayProperties.toJSON(e)
      );
    }
    if ((_e = message.booleanArrayProperties) === null || _e === void 0 ? void 0 : _e.length) {
      obj.booleanArrayProperties = message.booleanArrayProperties.map((e) =>
        base_js_1.BooleanArrayProperties.toJSON(e)
      );
    }
    if ((_f = message.objectProperties) === null || _f === void 0 ? void 0 : _f.length) {
      obj.objectProperties = message.objectProperties.map((e) => base_js_1.ObjectProperties.toJSON(e));
    }
    if ((_g = message.objectArrayProperties) === null || _g === void 0 ? void 0 : _g.length) {
      obj.objectArrayProperties = message.objectArrayProperties.map((e) =>
        base_js_1.ObjectArrayProperties.toJSON(e)
      );
    }
    if (message.nonRefProps !== undefined) {
      obj.nonRefProps = properties_js_1.Properties.toJSON(message.nonRefProps);
    }
    if (message.refPropsRequested !== false) {
      obj.refPropsRequested = message.refPropsRequested;
    }
    return obj;
  },
  create(base) {
    return exports.PropertiesResult.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;
    const message = createBasePropertiesResult();
    message.nonRefProperties = (_a = object.nonRefProperties) !== null && _a !== void 0 ? _a : undefined;
    message.refProps =
      ((_b = object.refProps) === null || _b === void 0
        ? void 0
        : _b.map((e) => exports.RefPropertiesResult.fromPartial(e))) || [];
    message.targetCollection = (_c = object.targetCollection) !== null && _c !== void 0 ? _c : '';
    message.metadata =
      object.metadata !== undefined && object.metadata !== null
        ? exports.MetadataResult.fromPartial(object.metadata)
        : undefined;
    message.numberArrayProperties =
      ((_d = object.numberArrayProperties) === null || _d === void 0
        ? void 0
        : _d.map((e) => base_js_1.NumberArrayProperties.fromPartial(e))) || [];
    message.intArrayProperties =
      ((_e = object.intArrayProperties) === null || _e === void 0
        ? void 0
        : _e.map((e) => base_js_1.IntArrayProperties.fromPartial(e))) || [];
    message.textArrayProperties =
      ((_f = object.textArrayProperties) === null || _f === void 0
        ? void 0
        : _f.map((e) => base_js_1.TextArrayProperties.fromPartial(e))) || [];
    message.booleanArrayProperties =
      ((_g = object.booleanArrayProperties) === null || _g === void 0
        ? void 0
        : _g.map((e) => base_js_1.BooleanArrayProperties.fromPartial(e))) || [];
    message.objectProperties =
      ((_h = object.objectProperties) === null || _h === void 0
        ? void 0
        : _h.map((e) => base_js_1.ObjectProperties.fromPartial(e))) || [];
    message.objectArrayProperties =
      ((_j = object.objectArrayProperties) === null || _j === void 0
        ? void 0
        : _j.map((e) => base_js_1.ObjectArrayProperties.fromPartial(e))) || [];
    message.nonRefProps =
      object.nonRefProps !== undefined && object.nonRefProps !== null
        ? properties_js_1.Properties.fromPartial(object.nonRefProps)
        : undefined;
    message.refPropsRequested = (_k = object.refPropsRequested) !== null && _k !== void 0 ? _k : false;
    return message;
  },
};
function createBaseRefPropertiesResult() {
  return { properties: [], propName: '' };
}
exports.RefPropertiesResult = {
  encode(message, writer = minimal_js_1.default.Writer.create()) {
    for (const v of message.properties) {
      exports.PropertiesResult.encode(v, writer.uint32(10).fork()).ldelim();
    }
    if (message.propName !== '') {
      writer.uint32(18).string(message.propName);
    }
    return writer;
  },
  decode(input, length) {
    const reader =
      input instanceof minimal_js_1.default.Reader ? input : minimal_js_1.default.Reader.create(input);
    let end = length === undefined ? reader.len : reader.pos + length;
    const message = createBaseRefPropertiesResult();
    while (reader.pos < end) {
      const tag = reader.uint32();
      switch (tag >>> 3) {
        case 1:
          if (tag !== 10) {
            break;
          }
          message.properties.push(exports.PropertiesResult.decode(reader, reader.uint32()));
          continue;
        case 2:
          if (tag !== 18) {
            break;
          }
          message.propName = reader.string();
          continue;
      }
      if ((tag & 7) === 4 || tag === 0) {
        break;
      }
      reader.skipType(tag & 7);
    }
    return message;
  },
  fromJSON(object) {
    return {
      properties: globalThis.Array.isArray(object === null || object === void 0 ? void 0 : object.properties)
        ? object.properties.map((e) => exports.PropertiesResult.fromJSON(e))
        : [],
      propName: isSet(object.propName) ? globalThis.String(object.propName) : '',
    };
  },
  toJSON(message) {
    var _a;
    const obj = {};
    if ((_a = message.properties) === null || _a === void 0 ? void 0 : _a.length) {
      obj.properties = message.properties.map((e) => exports.PropertiesResult.toJSON(e));
    }
    if (message.propName !== '') {
      obj.propName = message.propName;
    }
    return obj;
  },
  create(base) {
    return exports.RefPropertiesResult.fromPartial(base !== null && base !== void 0 ? base : {});
  },
  fromPartial(object) {
    var _a, _b;
    const message = createBaseRefPropertiesResult();
    message.properties =
      ((_a = object.properties) === null || _a === void 0
        ? void 0
        : _a.map((e) => exports.PropertiesResult.fromPartial(e))) || [];
    message.propName = (_b = object.propName) !== null && _b !== void 0 ? _b : '';
    return message;
  },
};
function bytesFromBase64(b64) {
  if (globalThis.Buffer) {
    return Uint8Array.from(globalThis.Buffer.from(b64, 'base64'));
  } else {
    const bin = globalThis.atob(b64);
    const arr = new Uint8Array(bin.length);
    for (let i = 0; i < bin.length; ++i) {
      arr[i] = bin.charCodeAt(i);
    }
    return arr;
  }
}
function base64FromBytes(arr) {
  if (globalThis.Buffer) {
    return globalThis.Buffer.from(arr).toString('base64');
  } else {
    const bin = [];
    arr.forEach((byte) => {
      bin.push(globalThis.String.fromCharCode(byte));
    });
    return globalThis.btoa(bin.join(''));
  }
}
function longToNumber(long) {
  if (long.gt(globalThis.Number.MAX_SAFE_INTEGER)) {
    throw new globalThis.Error('Value is larger than Number.MAX_SAFE_INTEGER');
  }
  return long.toNumber();
}
if (minimal_js_1.default.util.Long !== long_1.default) {
  minimal_js_1.default.util.Long = long_1.default;
  minimal_js_1.default.configure();
}
function isObject(value) {
  return typeof value === 'object' && value !== null;
}
function isSet(value) {
  return value !== null && value !== undefined;
}
