"use strict";

var _interopRequireDefault = require("@babel/runtime/helpers/interopRequireDefault");
Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "abnf", {
  enumerable: true,
  get: function get() {
    return _abnf["default"];
  }
});
Object.defineProperty(exports, "accesslog", {
  enumerable: true,
  get: function get() {
    return _accesslog["default"];
  }
});
Object.defineProperty(exports, "actionscript", {
  enumerable: true,
  get: function get() {
    return _actionscript["default"];
  }
});
Object.defineProperty(exports, "ada", {
  enumerable: true,
  get: function get() {
    return _ada["default"];
  }
});
Object.defineProperty(exports, "angelscript", {
  enumerable: true,
  get: function get() {
    return _angelscript["default"];
  }
});
Object.defineProperty(exports, "apache", {
  enumerable: true,
  get: function get() {
    return _apache["default"];
  }
});
Object.defineProperty(exports, "applescript", {
  enumerable: true,
  get: function get() {
    return _applescript["default"];
  }
});
Object.defineProperty(exports, "arcade", {
  enumerable: true,
  get: function get() {
    return _arcade["default"];
  }
});
Object.defineProperty(exports, "arduino", {
  enumerable: true,
  get: function get() {
    return _arduino["default"];
  }
});
Object.defineProperty(exports, "armasm", {
  enumerable: true,
  get: function get() {
    return _armasm["default"];
  }
});
Object.defineProperty(exports, "asciidoc", {
  enumerable: true,
  get: function get() {
    return _asciidoc["default"];
  }
});
Object.defineProperty(exports, "aspectj", {
  enumerable: true,
  get: function get() {
    return _aspectj["default"];
  }
});
Object.defineProperty(exports, "autohotkey", {
  enumerable: true,
  get: function get() {
    return _autohotkey["default"];
  }
});
Object.defineProperty(exports, "autoit", {
  enumerable: true,
  get: function get() {
    return _autoit["default"];
  }
});
Object.defineProperty(exports, "avrasm", {
  enumerable: true,
  get: function get() {
    return _avrasm["default"];
  }
});
Object.defineProperty(exports, "awk", {
  enumerable: true,
  get: function get() {
    return _awk["default"];
  }
});
Object.defineProperty(exports, "axapta", {
  enumerable: true,
  get: function get() {
    return _axapta["default"];
  }
});
Object.defineProperty(exports, "bash", {
  enumerable: true,
  get: function get() {
    return _bash["default"];
  }
});
Object.defineProperty(exports, "basic", {
  enumerable: true,
  get: function get() {
    return _basic["default"];
  }
});
Object.defineProperty(exports, "bnf", {
  enumerable: true,
  get: function get() {
    return _bnf["default"];
  }
});
Object.defineProperty(exports, "brainfuck", {
  enumerable: true,
  get: function get() {
    return _brainfuck["default"];
  }
});
Object.defineProperty(exports, "c", {
  enumerable: true,
  get: function get() {
    return _c2["default"];
  }
});
Object.defineProperty(exports, "cLike", {
  enumerable: true,
  get: function get() {
    return _cLike["default"];
  }
});
Object.defineProperty(exports, "cal", {
  enumerable: true,
  get: function get() {
    return _cal["default"];
  }
});
Object.defineProperty(exports, "capnproto", {
  enumerable: true,
  get: function get() {
    return _capnproto["default"];
  }
});
Object.defineProperty(exports, "ceylon", {
  enumerable: true,
  get: function get() {
    return _ceylon["default"];
  }
});
Object.defineProperty(exports, "clean", {
  enumerable: true,
  get: function get() {
    return _clean["default"];
  }
});
Object.defineProperty(exports, "clojure", {
  enumerable: true,
  get: function get() {
    return _clojure["default"];
  }
});
Object.defineProperty(exports, "clojureRepl", {
  enumerable: true,
  get: function get() {
    return _clojureRepl["default"];
  }
});
Object.defineProperty(exports, "cmake", {
  enumerable: true,
  get: function get() {
    return _cmake["default"];
  }
});
Object.defineProperty(exports, "coffeescript", {
  enumerable: true,
  get: function get() {
    return _coffeescript["default"];
  }
});
Object.defineProperty(exports, "coq", {
  enumerable: true,
  get: function get() {
    return _coq["default"];
  }
});
Object.defineProperty(exports, "cos", {
  enumerable: true,
  get: function get() {
    return _cos["default"];
  }
});
Object.defineProperty(exports, "cpp", {
  enumerable: true,
  get: function get() {
    return _cpp["default"];
  }
});
Object.defineProperty(exports, "crmsh", {
  enumerable: true,
  get: function get() {
    return _crmsh["default"];
  }
});
Object.defineProperty(exports, "crystal", {
  enumerable: true,
  get: function get() {
    return _crystal["default"];
  }
});
Object.defineProperty(exports, "csharp", {
  enumerable: true,
  get: function get() {
    return _csharp["default"];
  }
});
Object.defineProperty(exports, "csp", {
  enumerable: true,
  get: function get() {
    return _csp["default"];
  }
});
Object.defineProperty(exports, "css", {
  enumerable: true,
  get: function get() {
    return _css["default"];
  }
});
Object.defineProperty(exports, "d", {
  enumerable: true,
  get: function get() {
    return _d["default"];
  }
});
Object.defineProperty(exports, "dart", {
  enumerable: true,
  get: function get() {
    return _dart["default"];
  }
});
Object.defineProperty(exports, "delphi", {
  enumerable: true,
  get: function get() {
    return _delphi["default"];
  }
});
Object.defineProperty(exports, "diff", {
  enumerable: true,
  get: function get() {
    return _diff["default"];
  }
});
Object.defineProperty(exports, "django", {
  enumerable: true,
  get: function get() {
    return _django["default"];
  }
});
Object.defineProperty(exports, "dns", {
  enumerable: true,
  get: function get() {
    return _dns["default"];
  }
});
Object.defineProperty(exports, "dockerfile", {
  enumerable: true,
  get: function get() {
    return _dockerfile["default"];
  }
});
Object.defineProperty(exports, "dos", {
  enumerable: true,
  get: function get() {
    return _dos["default"];
  }
});
Object.defineProperty(exports, "dsconfig", {
  enumerable: true,
  get: function get() {
    return _dsconfig["default"];
  }
});
Object.defineProperty(exports, "dts", {
  enumerable: true,
  get: function get() {
    return _dts["default"];
  }
});
Object.defineProperty(exports, "dust", {
  enumerable: true,
  get: function get() {
    return _dust["default"];
  }
});
Object.defineProperty(exports, "ebnf", {
  enumerable: true,
  get: function get() {
    return _ebnf["default"];
  }
});
Object.defineProperty(exports, "elixir", {
  enumerable: true,
  get: function get() {
    return _elixir["default"];
  }
});
Object.defineProperty(exports, "elm", {
  enumerable: true,
  get: function get() {
    return _elm["default"];
  }
});
Object.defineProperty(exports, "erb", {
  enumerable: true,
  get: function get() {
    return _erb["default"];
  }
});
Object.defineProperty(exports, "erlang", {
  enumerable: true,
  get: function get() {
    return _erlang["default"];
  }
});
Object.defineProperty(exports, "erlangRepl", {
  enumerable: true,
  get: function get() {
    return _erlangRepl["default"];
  }
});
Object.defineProperty(exports, "excel", {
  enumerable: true,
  get: function get() {
    return _excel["default"];
  }
});
Object.defineProperty(exports, "fix", {
  enumerable: true,
  get: function get() {
    return _fix["default"];
  }
});
Object.defineProperty(exports, "flix", {
  enumerable: true,
  get: function get() {
    return _flix["default"];
  }
});
Object.defineProperty(exports, "fortran", {
  enumerable: true,
  get: function get() {
    return _fortran["default"];
  }
});
Object.defineProperty(exports, "fsharp", {
  enumerable: true,
  get: function get() {
    return _fsharp["default"];
  }
});
Object.defineProperty(exports, "gams", {
  enumerable: true,
  get: function get() {
    return _gams["default"];
  }
});
Object.defineProperty(exports, "gauss", {
  enumerable: true,
  get: function get() {
    return _gauss["default"];
  }
});
Object.defineProperty(exports, "gcode", {
  enumerable: true,
  get: function get() {
    return _gcode["default"];
  }
});
Object.defineProperty(exports, "gherkin", {
  enumerable: true,
  get: function get() {
    return _gherkin["default"];
  }
});
Object.defineProperty(exports, "glsl", {
  enumerable: true,
  get: function get() {
    return _glsl["default"];
  }
});
Object.defineProperty(exports, "gml", {
  enumerable: true,
  get: function get() {
    return _gml["default"];
  }
});
Object.defineProperty(exports, "go", {
  enumerable: true,
  get: function get() {
    return _go["default"];
  }
});
Object.defineProperty(exports, "golo", {
  enumerable: true,
  get: function get() {
    return _golo["default"];
  }
});
Object.defineProperty(exports, "gradle", {
  enumerable: true,
  get: function get() {
    return _gradle["default"];
  }
});
Object.defineProperty(exports, "groovy", {
  enumerable: true,
  get: function get() {
    return _groovy["default"];
  }
});
Object.defineProperty(exports, "haml", {
  enumerable: true,
  get: function get() {
    return _haml["default"];
  }
});
Object.defineProperty(exports, "handlebars", {
  enumerable: true,
  get: function get() {
    return _handlebars["default"];
  }
});
Object.defineProperty(exports, "haskell", {
  enumerable: true,
  get: function get() {
    return _haskell["default"];
  }
});
Object.defineProperty(exports, "haxe", {
  enumerable: true,
  get: function get() {
    return _haxe["default"];
  }
});
Object.defineProperty(exports, "hsp", {
  enumerable: true,
  get: function get() {
    return _hsp["default"];
  }
});
Object.defineProperty(exports, "htmlbars", {
  enumerable: true,
  get: function get() {
    return _htmlbars["default"];
  }
});
Object.defineProperty(exports, "http", {
  enumerable: true,
  get: function get() {
    return _http["default"];
  }
});
Object.defineProperty(exports, "hy", {
  enumerable: true,
  get: function get() {
    return _hy["default"];
  }
});
Object.defineProperty(exports, "inform7", {
  enumerable: true,
  get: function get() {
    return _inform["default"];
  }
});
Object.defineProperty(exports, "ini", {
  enumerable: true,
  get: function get() {
    return _ini["default"];
  }
});
Object.defineProperty(exports, "irpf90", {
  enumerable: true,
  get: function get() {
    return _irpf["default"];
  }
});
Object.defineProperty(exports, "isbl", {
  enumerable: true,
  get: function get() {
    return _isbl["default"];
  }
});
Object.defineProperty(exports, "java", {
  enumerable: true,
  get: function get() {
    return _java["default"];
  }
});
Object.defineProperty(exports, "javascript", {
  enumerable: true,
  get: function get() {
    return _javascript["default"];
  }
});
Object.defineProperty(exports, "jbossCli", {
  enumerable: true,
  get: function get() {
    return _jbossCli["default"];
  }
});
Object.defineProperty(exports, "json", {
  enumerable: true,
  get: function get() {
    return _json["default"];
  }
});
Object.defineProperty(exports, "julia", {
  enumerable: true,
  get: function get() {
    return _julia["default"];
  }
});
Object.defineProperty(exports, "juliaRepl", {
  enumerable: true,
  get: function get() {
    return _juliaRepl["default"];
  }
});
Object.defineProperty(exports, "kotlin", {
  enumerable: true,
  get: function get() {
    return _kotlin["default"];
  }
});
Object.defineProperty(exports, "lasso", {
  enumerable: true,
  get: function get() {
    return _lasso["default"];
  }
});
Object.defineProperty(exports, "latex", {
  enumerable: true,
  get: function get() {
    return _latex["default"];
  }
});
Object.defineProperty(exports, "ldif", {
  enumerable: true,
  get: function get() {
    return _ldif["default"];
  }
});
Object.defineProperty(exports, "leaf", {
  enumerable: true,
  get: function get() {
    return _leaf["default"];
  }
});
Object.defineProperty(exports, "less", {
  enumerable: true,
  get: function get() {
    return _less["default"];
  }
});
Object.defineProperty(exports, "lisp", {
  enumerable: true,
  get: function get() {
    return _lisp["default"];
  }
});
Object.defineProperty(exports, "livecodeserver", {
  enumerable: true,
  get: function get() {
    return _livecodeserver["default"];
  }
});
Object.defineProperty(exports, "livescript", {
  enumerable: true,
  get: function get() {
    return _livescript["default"];
  }
});
Object.defineProperty(exports, "llvm", {
  enumerable: true,
  get: function get() {
    return _llvm["default"];
  }
});
Object.defineProperty(exports, "lsl", {
  enumerable: true,
  get: function get() {
    return _lsl["default"];
  }
});
Object.defineProperty(exports, "lua", {
  enumerable: true,
  get: function get() {
    return _lua["default"];
  }
});
Object.defineProperty(exports, "makefile", {
  enumerable: true,
  get: function get() {
    return _makefile["default"];
  }
});
Object.defineProperty(exports, "markdown", {
  enumerable: true,
  get: function get() {
    return _markdown["default"];
  }
});
Object.defineProperty(exports, "mathematica", {
  enumerable: true,
  get: function get() {
    return _mathematica["default"];
  }
});
Object.defineProperty(exports, "matlab", {
  enumerable: true,
  get: function get() {
    return _matlab["default"];
  }
});
Object.defineProperty(exports, "maxima", {
  enumerable: true,
  get: function get() {
    return _maxima["default"];
  }
});
Object.defineProperty(exports, "mel", {
  enumerable: true,
  get: function get() {
    return _mel["default"];
  }
});
Object.defineProperty(exports, "mercury", {
  enumerable: true,
  get: function get() {
    return _mercury["default"];
  }
});
Object.defineProperty(exports, "mipsasm", {
  enumerable: true,
  get: function get() {
    return _mipsasm["default"];
  }
});
Object.defineProperty(exports, "mizar", {
  enumerable: true,
  get: function get() {
    return _mizar["default"];
  }
});
Object.defineProperty(exports, "mojolicious", {
  enumerable: true,
  get: function get() {
    return _mojolicious["default"];
  }
});
Object.defineProperty(exports, "monkey", {
  enumerable: true,
  get: function get() {
    return _monkey["default"];
  }
});
Object.defineProperty(exports, "moonscript", {
  enumerable: true,
  get: function get() {
    return _moonscript["default"];
  }
});
Object.defineProperty(exports, "n1ql", {
  enumerable: true,
  get: function get() {
    return _n1ql["default"];
  }
});
Object.defineProperty(exports, "nginx", {
  enumerable: true,
  get: function get() {
    return _nginx["default"];
  }
});
Object.defineProperty(exports, "nim", {
  enumerable: true,
  get: function get() {
    return _nim["default"];
  }
});
Object.defineProperty(exports, "nix", {
  enumerable: true,
  get: function get() {
    return _nix["default"];
  }
});
Object.defineProperty(exports, "nodeRepl", {
  enumerable: true,
  get: function get() {
    return _nodeRepl["default"];
  }
});
Object.defineProperty(exports, "nsis", {
  enumerable: true,
  get: function get() {
    return _nsis["default"];
  }
});
Object.defineProperty(exports, "objectivec", {
  enumerable: true,
  get: function get() {
    return _objectivec["default"];
  }
});
Object.defineProperty(exports, "ocaml", {
  enumerable: true,
  get: function get() {
    return _ocaml["default"];
  }
});
Object.defineProperty(exports, "oneC", {
  enumerable: true,
  get: function get() {
    return _c["default"];
  }
});
Object.defineProperty(exports, "openscad", {
  enumerable: true,
  get: function get() {
    return _openscad["default"];
  }
});
Object.defineProperty(exports, "oxygene", {
  enumerable: true,
  get: function get() {
    return _oxygene["default"];
  }
});
Object.defineProperty(exports, "parser3", {
  enumerable: true,
  get: function get() {
    return _parser["default"];
  }
});
Object.defineProperty(exports, "perl", {
  enumerable: true,
  get: function get() {
    return _perl["default"];
  }
});
Object.defineProperty(exports, "pf", {
  enumerable: true,
  get: function get() {
    return _pf["default"];
  }
});
Object.defineProperty(exports, "pgsql", {
  enumerable: true,
  get: function get() {
    return _pgsql["default"];
  }
});
Object.defineProperty(exports, "php", {
  enumerable: true,
  get: function get() {
    return _php["default"];
  }
});
Object.defineProperty(exports, "phpTemplate", {
  enumerable: true,
  get: function get() {
    return _phpTemplate["default"];
  }
});
Object.defineProperty(exports, "plaintext", {
  enumerable: true,
  get: function get() {
    return _plaintext["default"];
  }
});
Object.defineProperty(exports, "pony", {
  enumerable: true,
  get: function get() {
    return _pony["default"];
  }
});
Object.defineProperty(exports, "powershell", {
  enumerable: true,
  get: function get() {
    return _powershell["default"];
  }
});
Object.defineProperty(exports, "processing", {
  enumerable: true,
  get: function get() {
    return _processing["default"];
  }
});
Object.defineProperty(exports, "profile", {
  enumerable: true,
  get: function get() {
    return _profile["default"];
  }
});
Object.defineProperty(exports, "prolog", {
  enumerable: true,
  get: function get() {
    return _prolog["default"];
  }
});
Object.defineProperty(exports, "properties", {
  enumerable: true,
  get: function get() {
    return _properties["default"];
  }
});
Object.defineProperty(exports, "protobuf", {
  enumerable: true,
  get: function get() {
    return _protobuf["default"];
  }
});
Object.defineProperty(exports, "puppet", {
  enumerable: true,
  get: function get() {
    return _puppet["default"];
  }
});
Object.defineProperty(exports, "purebasic", {
  enumerable: true,
  get: function get() {
    return _purebasic["default"];
  }
});
Object.defineProperty(exports, "python", {
  enumerable: true,
  get: function get() {
    return _python["default"];
  }
});
Object.defineProperty(exports, "pythonRepl", {
  enumerable: true,
  get: function get() {
    return _pythonRepl["default"];
  }
});
Object.defineProperty(exports, "q", {
  enumerable: true,
  get: function get() {
    return _q["default"];
  }
});
Object.defineProperty(exports, "qml", {
  enumerable: true,
  get: function get() {
    return _qml["default"];
  }
});
Object.defineProperty(exports, "r", {
  enumerable: true,
  get: function get() {
    return _r["default"];
  }
});
Object.defineProperty(exports, "reasonml", {
  enumerable: true,
  get: function get() {
    return _reasonml["default"];
  }
});
Object.defineProperty(exports, "rib", {
  enumerable: true,
  get: function get() {
    return _rib["default"];
  }
});
Object.defineProperty(exports, "roboconf", {
  enumerable: true,
  get: function get() {
    return _roboconf["default"];
  }
});
Object.defineProperty(exports, "routeros", {
  enumerable: true,
  get: function get() {
    return _routeros["default"];
  }
});
Object.defineProperty(exports, "rsl", {
  enumerable: true,
  get: function get() {
    return _rsl["default"];
  }
});
Object.defineProperty(exports, "ruby", {
  enumerable: true,
  get: function get() {
    return _ruby["default"];
  }
});
Object.defineProperty(exports, "ruleslanguage", {
  enumerable: true,
  get: function get() {
    return _ruleslanguage["default"];
  }
});
Object.defineProperty(exports, "rust", {
  enumerable: true,
  get: function get() {
    return _rust["default"];
  }
});
Object.defineProperty(exports, "sas", {
  enumerable: true,
  get: function get() {
    return _sas["default"];
  }
});
Object.defineProperty(exports, "scala", {
  enumerable: true,
  get: function get() {
    return _scala["default"];
  }
});
Object.defineProperty(exports, "scheme", {
  enumerable: true,
  get: function get() {
    return _scheme["default"];
  }
});
Object.defineProperty(exports, "scilab", {
  enumerable: true,
  get: function get() {
    return _scilab["default"];
  }
});
Object.defineProperty(exports, "scss", {
  enumerable: true,
  get: function get() {
    return _scss["default"];
  }
});
Object.defineProperty(exports, "shell", {
  enumerable: true,
  get: function get() {
    return _shell["default"];
  }
});
Object.defineProperty(exports, "smali", {
  enumerable: true,
  get: function get() {
    return _smali["default"];
  }
});
Object.defineProperty(exports, "smalltalk", {
  enumerable: true,
  get: function get() {
    return _smalltalk["default"];
  }
});
Object.defineProperty(exports, "sml", {
  enumerable: true,
  get: function get() {
    return _sml["default"];
  }
});
Object.defineProperty(exports, "sqf", {
  enumerable: true,
  get: function get() {
    return _sqf["default"];
  }
});
Object.defineProperty(exports, "sql", {
  enumerable: true,
  get: function get() {
    return _sql["default"];
  }
});
Object.defineProperty(exports, "sqlMore", {
  enumerable: true,
  get: function get() {
    return _sql_more["default"];
  }
});
Object.defineProperty(exports, "stan", {
  enumerable: true,
  get: function get() {
    return _stan["default"];
  }
});
Object.defineProperty(exports, "stata", {
  enumerable: true,
  get: function get() {
    return _stata["default"];
  }
});
Object.defineProperty(exports, "step21", {
  enumerable: true,
  get: function get() {
    return _step["default"];
  }
});
Object.defineProperty(exports, "stylus", {
  enumerable: true,
  get: function get() {
    return _stylus["default"];
  }
});
Object.defineProperty(exports, "subunit", {
  enumerable: true,
  get: function get() {
    return _subunit["default"];
  }
});
Object.defineProperty(exports, "swift", {
  enumerable: true,
  get: function get() {
    return _swift["default"];
  }
});
Object.defineProperty(exports, "taggerscript", {
  enumerable: true,
  get: function get() {
    return _taggerscript["default"];
  }
});
Object.defineProperty(exports, "tap", {
  enumerable: true,
  get: function get() {
    return _tap["default"];
  }
});
Object.defineProperty(exports, "tcl", {
  enumerable: true,
  get: function get() {
    return _tcl["default"];
  }
});
Object.defineProperty(exports, "thrift", {
  enumerable: true,
  get: function get() {
    return _thrift["default"];
  }
});
Object.defineProperty(exports, "tp", {
  enumerable: true,
  get: function get() {
    return _tp["default"];
  }
});
Object.defineProperty(exports, "twig", {
  enumerable: true,
  get: function get() {
    return _twig["default"];
  }
});
Object.defineProperty(exports, "typescript", {
  enumerable: true,
  get: function get() {
    return _typescript["default"];
  }
});
Object.defineProperty(exports, "vala", {
  enumerable: true,
  get: function get() {
    return _vala["default"];
  }
});
Object.defineProperty(exports, "vbnet", {
  enumerable: true,
  get: function get() {
    return _vbnet["default"];
  }
});
Object.defineProperty(exports, "vbscript", {
  enumerable: true,
  get: function get() {
    return _vbscript["default"];
  }
});
Object.defineProperty(exports, "vbscriptHtml", {
  enumerable: true,
  get: function get() {
    return _vbscriptHtml["default"];
  }
});
Object.defineProperty(exports, "verilog", {
  enumerable: true,
  get: function get() {
    return _verilog["default"];
  }
});
Object.defineProperty(exports, "vhdl", {
  enumerable: true,
  get: function get() {
    return _vhdl["default"];
  }
});
Object.defineProperty(exports, "vim", {
  enumerable: true,
  get: function get() {
    return _vim["default"];
  }
});
Object.defineProperty(exports, "x86asm", {
  enumerable: true,
  get: function get() {
    return _x86asm["default"];
  }
});
Object.defineProperty(exports, "xl", {
  enumerable: true,
  get: function get() {
    return _xl["default"];
  }
});
Object.defineProperty(exports, "xml", {
  enumerable: true,
  get: function get() {
    return _xml["default"];
  }
});
Object.defineProperty(exports, "xquery", {
  enumerable: true,
  get: function get() {
    return _xquery["default"];
  }
});
Object.defineProperty(exports, "yaml", {
  enumerable: true,
  get: function get() {
    return _yaml["default"];
  }
});
Object.defineProperty(exports, "zephir", {
  enumerable: true,
  get: function get() {
    return _zephir["default"];
  }
});
var _c = _interopRequireDefault(require("./1c"));
var _abnf = _interopRequireDefault(require("./abnf"));
var _accesslog = _interopRequireDefault(require("./accesslog"));
var _actionscript = _interopRequireDefault(require("./actionscript"));
var _ada = _interopRequireDefault(require("./ada"));
var _angelscript = _interopRequireDefault(require("./angelscript"));
var _apache = _interopRequireDefault(require("./apache"));
var _applescript = _interopRequireDefault(require("./applescript"));
var _arcade = _interopRequireDefault(require("./arcade"));
var _arduino = _interopRequireDefault(require("./arduino"));
var _armasm = _interopRequireDefault(require("./armasm"));
var _asciidoc = _interopRequireDefault(require("./asciidoc"));
var _aspectj = _interopRequireDefault(require("./aspectj"));
var _autohotkey = _interopRequireDefault(require("./autohotkey"));
var _autoit = _interopRequireDefault(require("./autoit"));
var _avrasm = _interopRequireDefault(require("./avrasm"));
var _awk = _interopRequireDefault(require("./awk"));
var _axapta = _interopRequireDefault(require("./axapta"));
var _bash = _interopRequireDefault(require("./bash"));
var _basic = _interopRequireDefault(require("./basic"));
var _bnf = _interopRequireDefault(require("./bnf"));
var _brainfuck = _interopRequireDefault(require("./brainfuck"));
var _cLike = _interopRequireDefault(require("./c-like"));
var _c2 = _interopRequireDefault(require("./c"));
var _cal = _interopRequireDefault(require("./cal"));
var _capnproto = _interopRequireDefault(require("./capnproto"));
var _ceylon = _interopRequireDefault(require("./ceylon"));
var _clean = _interopRequireDefault(require("./clean"));
var _clojureRepl = _interopRequireDefault(require("./clojure-repl"));
var _clojure = _interopRequireDefault(require("./clojure"));
var _cmake = _interopRequireDefault(require("./cmake"));
var _coffeescript = _interopRequireDefault(require("./coffeescript"));
var _coq = _interopRequireDefault(require("./coq"));
var _cos = _interopRequireDefault(require("./cos"));
var _cpp = _interopRequireDefault(require("./cpp"));
var _crmsh = _interopRequireDefault(require("./crmsh"));
var _crystal = _interopRequireDefault(require("./crystal"));
var _csharp = _interopRequireDefault(require("./csharp"));
var _csp = _interopRequireDefault(require("./csp"));
var _css = _interopRequireDefault(require("./css"));
var _d = _interopRequireDefault(require("./d"));
var _dart = _interopRequireDefault(require("./dart"));
var _delphi = _interopRequireDefault(require("./delphi"));
var _diff = _interopRequireDefault(require("./diff"));
var _django = _interopRequireDefault(require("./django"));
var _dns = _interopRequireDefault(require("./dns"));
var _dockerfile = _interopRequireDefault(require("./dockerfile"));
var _dos = _interopRequireDefault(require("./dos"));
var _dsconfig = _interopRequireDefault(require("./dsconfig"));
var _dts = _interopRequireDefault(require("./dts"));
var _dust = _interopRequireDefault(require("./dust"));
var _ebnf = _interopRequireDefault(require("./ebnf"));
var _elixir = _interopRequireDefault(require("./elixir"));
var _elm = _interopRequireDefault(require("./elm"));
var _erb = _interopRequireDefault(require("./erb"));
var _erlangRepl = _interopRequireDefault(require("./erlang-repl"));
var _erlang = _interopRequireDefault(require("./erlang"));
var _excel = _interopRequireDefault(require("./excel"));
var _fix = _interopRequireDefault(require("./fix"));
var _flix = _interopRequireDefault(require("./flix"));
var _fortran = _interopRequireDefault(require("./fortran"));
var _fsharp = _interopRequireDefault(require("./fsharp"));
var _gams = _interopRequireDefault(require("./gams"));
var _gauss = _interopRequireDefault(require("./gauss"));
var _gcode = _interopRequireDefault(require("./gcode"));
var _gherkin = _interopRequireDefault(require("./gherkin"));
var _glsl = _interopRequireDefault(require("./glsl"));
var _gml = _interopRequireDefault(require("./gml"));
var _go = _interopRequireDefault(require("./go"));
var _golo = _interopRequireDefault(require("./golo"));
var _gradle = _interopRequireDefault(require("./gradle"));
var _groovy = _interopRequireDefault(require("./groovy"));
var _haml = _interopRequireDefault(require("./haml"));
var _handlebars = _interopRequireDefault(require("./handlebars"));
var _haskell = _interopRequireDefault(require("./haskell"));
var _haxe = _interopRequireDefault(require("./haxe"));
var _hsp = _interopRequireDefault(require("./hsp"));
var _htmlbars = _interopRequireDefault(require("./htmlbars"));
var _http = _interopRequireDefault(require("./http"));
var _hy = _interopRequireDefault(require("./hy"));
var _inform = _interopRequireDefault(require("./inform7"));
var _ini = _interopRequireDefault(require("./ini"));
var _irpf = _interopRequireDefault(require("./irpf90"));
var _isbl = _interopRequireDefault(require("./isbl"));
var _java = _interopRequireDefault(require("./java"));
var _javascript = _interopRequireDefault(require("./javascript"));
var _jbossCli = _interopRequireDefault(require("./jboss-cli"));
var _json = _interopRequireDefault(require("./json"));
var _juliaRepl = _interopRequireDefault(require("./julia-repl"));
var _julia = _interopRequireDefault(require("./julia"));
var _kotlin = _interopRequireDefault(require("./kotlin"));
var _lasso = _interopRequireDefault(require("./lasso"));
var _latex = _interopRequireDefault(require("./latex"));
var _ldif = _interopRequireDefault(require("./ldif"));
var _leaf = _interopRequireDefault(require("./leaf"));
var _less = _interopRequireDefault(require("./less"));
var _lisp = _interopRequireDefault(require("./lisp"));
var _livecodeserver = _interopRequireDefault(require("./livecodeserver"));
var _livescript = _interopRequireDefault(require("./livescript"));
var _llvm = _interopRequireDefault(require("./llvm"));
var _lsl = _interopRequireDefault(require("./lsl"));
var _lua = _interopRequireDefault(require("./lua"));
var _makefile = _interopRequireDefault(require("./makefile"));
var _markdown = _interopRequireDefault(require("./markdown"));
var _mathematica = _interopRequireDefault(require("./mathematica"));
var _matlab = _interopRequireDefault(require("./matlab"));
var _maxima = _interopRequireDefault(require("./maxima"));
var _mel = _interopRequireDefault(require("./mel"));
var _mercury = _interopRequireDefault(require("./mercury"));
var _mipsasm = _interopRequireDefault(require("./mipsasm"));
var _mizar = _interopRequireDefault(require("./mizar"));
var _mojolicious = _interopRequireDefault(require("./mojolicious"));
var _monkey = _interopRequireDefault(require("./monkey"));
var _moonscript = _interopRequireDefault(require("./moonscript"));
var _n1ql = _interopRequireDefault(require("./n1ql"));
var _nginx = _interopRequireDefault(require("./nginx"));
var _nim = _interopRequireDefault(require("./nim"));
var _nix = _interopRequireDefault(require("./nix"));
var _nodeRepl = _interopRequireDefault(require("./node-repl"));
var _nsis = _interopRequireDefault(require("./nsis"));
var _objectivec = _interopRequireDefault(require("./objectivec"));
var _ocaml = _interopRequireDefault(require("./ocaml"));
var _openscad = _interopRequireDefault(require("./openscad"));
var _oxygene = _interopRequireDefault(require("./oxygene"));
var _parser = _interopRequireDefault(require("./parser3"));
var _perl = _interopRequireDefault(require("./perl"));
var _pf = _interopRequireDefault(require("./pf"));
var _pgsql = _interopRequireDefault(require("./pgsql"));
var _phpTemplate = _interopRequireDefault(require("./php-template"));
var _php = _interopRequireDefault(require("./php"));
var _plaintext = _interopRequireDefault(require("./plaintext"));
var _pony = _interopRequireDefault(require("./pony"));
var _powershell = _interopRequireDefault(require("./powershell"));
var _processing = _interopRequireDefault(require("./processing"));
var _profile = _interopRequireDefault(require("./profile"));
var _prolog = _interopRequireDefault(require("./prolog"));
var _properties = _interopRequireDefault(require("./properties"));
var _protobuf = _interopRequireDefault(require("./protobuf"));
var _puppet = _interopRequireDefault(require("./puppet"));
var _purebasic = _interopRequireDefault(require("./purebasic"));
var _pythonRepl = _interopRequireDefault(require("./python-repl"));
var _python = _interopRequireDefault(require("./python"));
var _q = _interopRequireDefault(require("./q"));
var _qml = _interopRequireDefault(require("./qml"));
var _r = _interopRequireDefault(require("./r"));
var _reasonml = _interopRequireDefault(require("./reasonml"));
var _rib = _interopRequireDefault(require("./rib"));
var _roboconf = _interopRequireDefault(require("./roboconf"));
var _routeros = _interopRequireDefault(require("./routeros"));
var _rsl = _interopRequireDefault(require("./rsl"));
var _ruby = _interopRequireDefault(require("./ruby"));
var _ruleslanguage = _interopRequireDefault(require("./ruleslanguage"));
var _rust = _interopRequireDefault(require("./rust"));
var _sas = _interopRequireDefault(require("./sas"));
var _scala = _interopRequireDefault(require("./scala"));
var _scheme = _interopRequireDefault(require("./scheme"));
var _scilab = _interopRequireDefault(require("./scilab"));
var _scss = _interopRequireDefault(require("./scss"));
var _shell = _interopRequireDefault(require("./shell"));
var _smali = _interopRequireDefault(require("./smali"));
var _smalltalk = _interopRequireDefault(require("./smalltalk"));
var _sml = _interopRequireDefault(require("./sml"));
var _sqf = _interopRequireDefault(require("./sqf"));
var _sql = _interopRequireDefault(require("./sql"));
var _sql_more = _interopRequireDefault(require("./sql_more"));
var _stan = _interopRequireDefault(require("./stan"));
var _stata = _interopRequireDefault(require("./stata"));
var _step = _interopRequireDefault(require("./step21"));
var _stylus = _interopRequireDefault(require("./stylus"));
var _subunit = _interopRequireDefault(require("./subunit"));
var _swift = _interopRequireDefault(require("./swift"));
var _taggerscript = _interopRequireDefault(require("./taggerscript"));
var _tap = _interopRequireDefault(require("./tap"));
var _tcl = _interopRequireDefault(require("./tcl"));
var _thrift = _interopRequireDefault(require("./thrift"));
var _tp = _interopRequireDefault(require("./tp"));
var _twig = _interopRequireDefault(require("./twig"));
var _typescript = _interopRequireDefault(require("./typescript"));
var _vala = _interopRequireDefault(require("./vala"));
var _vbnet = _interopRequireDefault(require("./vbnet"));
var _vbscriptHtml = _interopRequireDefault(require("./vbscript-html"));
var _vbscript = _interopRequireDefault(require("./vbscript"));
var _verilog = _interopRequireDefault(require("./verilog"));
var _vhdl = _interopRequireDefault(require("./vhdl"));
var _vim = _interopRequireDefault(require("./vim"));
var _x86asm = _interopRequireDefault(require("./x86asm"));
var _xl = _interopRequireDefault(require("./xl"));
var _xml = _interopRequireDefault(require("./xml"));
var _xquery = _interopRequireDefault(require("./xquery"));
var _yaml = _interopRequireDefault(require("./yaml"));
var _zephir = _interopRequireDefault(require("./zephir"));