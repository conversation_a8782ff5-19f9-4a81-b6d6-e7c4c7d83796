"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/routing-setup/[configId]/page",{

/***/ "(app-pages-browser)/./src/app/routing-setup/[configId]/page.tsx":
/*!***************************************************!*\
  !*** ./src/app/routing-setup/[configId]/page.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RoutingSetupConfigPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowLeftIcon,ArrowUpIcon,Bars3Icon,BoltIcon,CheckCircleIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Cog6ToothIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowLeftIcon,ArrowUpIcon,Bars3Icon,BoltIcon,CheckCircleIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowLeftIcon,ArrowUpIcon,Bars3Icon,BoltIcon,CheckCircleIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CircleStackIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowLeftIcon,ArrowUpIcon,Bars3Icon,BoltIcon,CheckCircleIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowLeftIcon,ArrowUpIcon,Bars3Icon,BoltIcon,CheckCircleIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CurrencyDollarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowLeftIcon,ArrowUpIcon,Bars3Icon,BoltIcon,CheckCircleIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Bars3Icon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowLeftIcon,ArrowUpIcon,Bars3Icon,BoltIcon,CheckCircleIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowLeftIcon,ArrowUpIcon,Bars3Icon,BoltIcon,CheckCircleIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowLeftIcon,ArrowUpIcon,Bars3Icon,BoltIcon,CheckCircleIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowDownIcon,ArrowLeftIcon,ArrowUpIcon,Bars3Icon,BoltIcon,CheckCircleIcon,CircleStackIcon,Cog6ToothIcon,CurrencyDollarIcon,ListBulletIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowLeftIcon.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/Reorder/Item.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/Reorder/Group.mjs\");\n/* harmony import */ var _hooks_useRoutingSetupPrefetch__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useRoutingSetupPrefetch */ \"(app-pages-browser)/./src/hooks/useRoutingSetupPrefetch.ts\");\n/* harmony import */ var _components_RoutingSetupLoadingSkeleton__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/RoutingSetupLoadingSkeleton */ \"(app-pages-browser)/./src/components/RoutingSetupLoadingSkeleton.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nconst ROUTING_STRATEGIES = [\n    {\n        id: 'none',\n        name: 'Default Behavior',\n        shortDescription: 'Automatic load balancing',\n        description: \"RouKey will automatically load balance across all keys assigned to this configuration with intra-request retries. No extra setup needed.\",\n        icon: _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"]\n    },\n    {\n        id: 'intelligent_role',\n        name: 'Intelligent Role Routing',\n        shortDescription: 'AI-powered role classification',\n        description: \"RouKey uses an LLM to classify the user's prompt and routes to a key associated with that role. If no match, uses the 'Default General Chat Model'.\",\n        icon: _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]\n    },\n    {\n        id: 'complexity_round_robin',\n        name: 'Complexity-Based Round-Robin',\n        shortDescription: 'Route by prompt complexity',\n        description: 'RouKey classifies prompt complexity (1-5) and round-robins among active keys assigned to that complexity. Searches proximal levels if no exact match.',\n        icon: _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"]\n    },\n    {\n        id: 'strict_fallback',\n        name: 'Strict Fallback',\n        shortDescription: 'Ordered failover sequence',\n        description: 'Define an ordered list of API keys. RouKey will try them in sequence until one succeeds.',\n        icon: _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"]\n    },\n    {\n        id: 'cost_optimized',\n        name: 'Cost-Optimized Routing',\n        shortDescription: 'Smart cost-performance balance',\n        description: 'RouKey intelligently balances cost and performance by routing simple tasks to cheaper models and complex tasks to premium models. Maximizes savings while ensuring quality.',\n        icon: _barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"]\n    }\n];\n// Draggable API Key Item Component for Strict Fallback\nfunction DraggableApiKeyItem(param) {\n    let { apiKey, index } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.ReorderItem, {\n        value: apiKey,\n        className: \"bg-white border border-gray-200 rounded-lg p-4 shadow-sm transition-all duration-200 hover:shadow-md hover:border-gray-300 cursor-grab active:cursor-grabbing\",\n        whileDrag: {\n            scale: 1.02,\n            boxShadow: \"0 10px 25px rgba(0, 0, 0, 0.15)\",\n            zIndex: 1000\n        },\n        dragListener: true,\n        dragControls: undefined,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-orange-50 border border-orange-200 rounded-lg flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-semibold text-orange-600\",\n                                children: index + 1\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-gray-900\",\n                                    children: apiKey.label\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-600\",\n                                    children: [\n                                        apiKey.provider,\n                                        \" - \",\n                                        apiKey.predefined_model_id\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 121,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 117,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-2 text-gray-400 hover:text-gray-600 transition-colors cursor-grab active:cursor-grabbing\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                            className: \"w-4 h-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 128,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 127,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, this);\n}\n_c = DraggableApiKeyItem;\n// Simplified API Key Item Component (for non-draggable lists)\nfunction SimpleApiKeyItem(param) {\n    let { apiKey, index, onMoveUp, onMoveDown } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n        className: \"bg-white border border-gray-200 rounded-lg p-4 shadow-sm transition-all duration-200 hover:shadow-md hover:border-gray-300\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-8 h-8 bg-orange-50 border border-orange-200 rounded-lg flex items-center justify-center\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-sm font-semibold text-orange-600\",\n                                children: index + 1\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 147,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-gray-900\",\n                                    children: apiKey.label\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xs text-gray-600\",\n                                    children: [\n                                        apiKey.provider,\n                                        \" - \",\n                                        apiKey.predefined_model_id\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 152,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 150,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 146,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-1\",\n                    children: [\n                        onMoveUp && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onMoveUp,\n                            className: \"p-1 text-gray-400 hover:text-orange-600 hover:bg-orange-50 rounded transition-all duration-200\",\n                            title: \"Move up\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 162,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 157,\n                            columnNumber: 13\n                        }, this),\n                        onMoveDown && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onMoveDown,\n                            className: \"p-1 text-gray-400 hover:text-orange-600 hover:bg-orange-50 rounded transition-all duration-200\",\n                            title: \"Move down\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                className: \"w-4 h-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 166,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 155,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n            lineNumber: 145,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n        lineNumber: 144,\n        columnNumber: 5\n    }, this);\n}\n_c1 = SimpleApiKeyItem;\nfunction RoutingSetupConfigPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const configId = params.configId;\n    // Prefetch hook\n    const { getCachedData, isCached } = (0,_hooks_useRoutingSetupPrefetch__WEBPACK_IMPORTED_MODULE_4__.useRoutingSetupPrefetch)();\n    // Smart back navigation using search params and fallback to referrer\n    const getBackUrl = ()=>{\n        // First, check if we have a 'from' parameter in the URL\n        const fromParam = searchParams.get('from');\n        if (fromParam === 'routing-setup') {\n            return '/routing-setup';\n        }\n        if (fromParam === 'model-config') {\n            return \"/my-models/\".concat(configId);\n        }\n        // Fallback to referrer detection for backward compatibility\n        if (true) {\n            const referrer = document.referrer;\n            const currentHost = window.location.host;\n            if (referrer && referrer.includes(currentHost)) {\n                try {\n                    const referrerUrl = new URL(referrer);\n                    const referrerPath = referrerUrl.pathname;\n                    // If they came from the main routing setup page (exact match)\n                    if (referrerPath === '/routing-setup') {\n                        return '/routing-setup';\n                    }\n                    // If they came from the model configuration page\n                    if (referrerPath === \"/my-models/\".concat(configId)) {\n                        return \"/my-models/\".concat(configId);\n                    }\n                    // If they came from the my-models list page\n                    if (referrerPath === '/my-models') {\n                        return \"/my-models/\".concat(configId);\n                    }\n                    // If they came from any other my-models page, go to this config's page\n                    if (referrerPath.startsWith('/my-models/') && !referrerPath.includes('/routing-setup')) {\n                        return \"/my-models/\".concat(configId);\n                    }\n                } catch (e) {\n                // Silently handle URL parsing errors\n                }\n            }\n        }\n        // Default fallback to model configuration page\n        return \"/my-models/\".concat(configId);\n    };\n    const getBackButtonText = ()=>{\n        // First, check if we have a 'from' parameter in the URL\n        const fromParam = searchParams.get('from');\n        if (fromParam === 'routing-setup') {\n            return 'Back to Routing Setup';\n        }\n        if (fromParam === 'model-config') {\n            return 'Back to Configuration';\n        }\n        // Fallback to referrer detection\n        if (true) {\n            const referrer = document.referrer;\n            const currentHost = window.location.host;\n            if (referrer && referrer.includes(currentHost)) {\n                try {\n                    const referrerUrl = new URL(referrer);\n                    const referrerPath = referrerUrl.pathname;\n                    if (referrerPath === '/routing-setup') {\n                        return 'Back to Routing Setup';\n                    }\n                    if (referrerPath === \"/my-models/\".concat(configId) || referrerPath === '/my-models' || referrerPath.startsWith('/my-models/')) {\n                        return 'Back to Configuration';\n                    }\n                } catch (e) {\n                // Silently handle URL parsing errors\n                }\n            }\n        }\n        // Default fallback text\n        return 'Back to Configuration';\n    };\n    const handleBackNavigation = ()=>{\n        if ( true && window.history.length > 1) {\n            // Try to use browser back if there's history\n            const backUrl = getBackUrl();\n            const referrer = document.referrer;\n            const currentHost = window.location.host;\n            // If referrer is from our app, use browser back for better UX\n            if (referrer && referrer.includes(currentHost)) {\n                router.back();\n                return;\n            }\n        }\n        // Fallback to programmatic navigation\n        router.push(getBackUrl());\n    };\n    const [configDetails, setConfigDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [successMessage, setSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [showOptimisticLoading, setShowOptimisticLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedStrategy, setSelectedStrategy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('none');\n    const [strategyParams, setStrategyParams] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [availableApiKeys, setAvailableApiKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoadingApiKeys, setIsLoadingApiKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // State specifically for strict_fallback strategy\n    const [orderedFallbackKeys, setOrderedFallbackKeys] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    // New State for Complexity-Based Round-Robin\n    const [selectedApiKeyForComplexity, setSelectedApiKeyForComplexity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Stores fetched assignments: { apiKeyId1: [1,2], apiKeyId2: [3] }\n    const [complexityAssignments, setComplexityAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Levels selected in UI for the current selectedApiKeyForComplexity\n    const [currentKeyComplexityLevels, setCurrentKeyComplexityLevels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isFetchingAssignments, setIsFetchingAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isSavingAssignments, setIsSavingAssignments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [assignmentsError, setAssignmentsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [assignmentsSuccessMessage, setAssignmentsSuccessMessage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // Temporarily disabled DnD sensors and handlers\n    // const sensors = useSensors(\n    //   useSensor(PointerSensor),\n    //   useSensor(KeyboardSensor, {\n    //     coordinateGetter: sortableKeyboardCoordinates,\n    //   })\n    // );\n    // const handleDragEnd = (event: DragEndEvent) => {\n    //   const { active, over } = event;\n    //   if (active.id !== over?.id) {\n    //     const oldIndex = orderedFallbackKeys.findIndex((key) => key.id === active.id);\n    //     const newIndex = orderedFallbackKeys.findIndex((key) => key.id === over?.id);\n    //     const newOrderedKeys = arrayMove(orderedFallbackKeys, oldIndex, newIndex);\n    //     setOrderedFallbackKeys(newOrderedKeys);\n    //     // Update strategyParams immediately for saving\n    //     setStrategyParams({ ordered_api_key_ids: newOrderedKeys.map(k => k.id) });\n    //   }\n    // };\n    const fetchConfigAndKeys = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RoutingSetupConfigPage.useCallback[fetchConfigAndKeys]\": async ()=>{\n            if (!configId) {\n                setError('Configuration ID is missing.');\n                setIsLoading(false);\n                return;\n            }\n            // Check for cached data first\n            const cachedData = getCachedData(configId);\n            if (cachedData && cachedData.configDetails && cachedData.apiKeys) {\n                var _cachedData_routingParams;\n                console.log(\"⚡ [ROUTING SETUP] Using cached data for config: \".concat(configId));\n                setConfigDetails(cachedData.configDetails);\n                setAvailableApiKeys(cachedData.apiKeys);\n                const currentSelectedStrategy = cachedData.routingStrategy || 'none';\n                setSelectedStrategy(currentSelectedStrategy);\n                setStrategyParams(cachedData.routingParams || {});\n                // Initialize orderedFallbackKeys based on cached data\n                if (currentSelectedStrategy === 'strict_fallback' && ((_cachedData_routingParams = cachedData.routingParams) === null || _cachedData_routingParams === void 0 ? void 0 : _cachedData_routingParams.ordered_api_key_ids)) {\n                    const savedOrder = cachedData.routingParams.ordered_api_key_ids;\n                    const reorderedKeys = savedOrder.map({\n                        \"RoutingSetupConfigPage.useCallback[fetchConfigAndKeys].reorderedKeys\": (id)=>cachedData.apiKeys.find({\n                                \"RoutingSetupConfigPage.useCallback[fetchConfigAndKeys].reorderedKeys\": (k)=>k.id === id\n                            }[\"RoutingSetupConfigPage.useCallback[fetchConfigAndKeys].reorderedKeys\"])\n                    }[\"RoutingSetupConfigPage.useCallback[fetchConfigAndKeys].reorderedKeys\"]).filter(Boolean);\n                    const remainingKeys = cachedData.apiKeys.filter({\n                        \"RoutingSetupConfigPage.useCallback[fetchConfigAndKeys].remainingKeys\": (k)=>!savedOrder.includes(k.id)\n                    }[\"RoutingSetupConfigPage.useCallback[fetchConfigAndKeys].remainingKeys\"]);\n                    setOrderedFallbackKeys([\n                        ...reorderedKeys,\n                        ...remainingKeys\n                    ]);\n                } else {\n                    setOrderedFallbackKeys([\n                        ...cachedData.apiKeys\n                    ]);\n                }\n                setIsLoading(false);\n                setIsLoadingApiKeys(false);\n                return;\n            }\n            // Show optimistic loading for first-time visits\n            if (!isCached(configId)) {\n                setShowOptimisticLoading(true);\n            }\n            setIsLoading(true);\n            setIsLoadingApiKeys(true);\n            setError(null);\n            setSuccessMessage(null);\n            try {\n                // Fetch config details\n                const configRes = await fetch(\"/api/custom-configs/\".concat(configId));\n                if (!configRes.ok) {\n                    const errData = await configRes.json();\n                    throw new Error(errData.error || 'Failed to fetch configuration');\n                }\n                const currentConfig = await configRes.json();\n                setConfigDetails(currentConfig);\n                const currentSelectedStrategy = currentConfig.routing_strategy || 'none';\n                setSelectedStrategy(currentSelectedStrategy);\n                const currentStrategyParams = currentConfig.routing_strategy_params || {};\n                setStrategyParams(currentStrategyParams); // General params state\n                // Fetch API keys for this configuration\n                const keysRes = await fetch(\"/api/keys?custom_config_id=\".concat(configId));\n                if (!keysRes.ok) {\n                    const errData = await keysRes.json();\n                    throw new Error(errData.error || 'Failed to fetch API keys for this configuration');\n                }\n                const keys = await keysRes.json();\n                setAvailableApiKeys(keys);\n                // Initialize orderedFallbackKeys based on fetched keys and saved params\n                if (currentSelectedStrategy === 'strict_fallback' && currentStrategyParams.ordered_api_key_ids) {\n                    const savedOrder = currentStrategyParams.ordered_api_key_ids;\n                    const reorderedKeys = savedOrder.map({\n                        \"RoutingSetupConfigPage.useCallback[fetchConfigAndKeys].reorderedKeys\": (id)=>keys.find({\n                                \"RoutingSetupConfigPage.useCallback[fetchConfigAndKeys].reorderedKeys\": (k)=>k.id === id\n                            }[\"RoutingSetupConfigPage.useCallback[fetchConfigAndKeys].reorderedKeys\"])\n                    }[\"RoutingSetupConfigPage.useCallback[fetchConfigAndKeys].reorderedKeys\"]).filter(Boolean);\n                    // Add any keys present in `keys` but not in `savedOrder` to the end\n                    const remainingKeys = keys.filter({\n                        \"RoutingSetupConfigPage.useCallback[fetchConfigAndKeys].remainingKeys\": (k)=>!savedOrder.includes(k.id)\n                    }[\"RoutingSetupConfigPage.useCallback[fetchConfigAndKeys].remainingKeys\"]);\n                    setOrderedFallbackKeys([\n                        ...reorderedKeys,\n                        ...remainingKeys\n                    ]);\n                } else {\n                    setOrderedFallbackKeys([\n                        ...keys\n                    ]); // Default order or for other strategies initially\n                }\n            } catch (err) {\n                setError(\"Error loading data: \".concat(err.message));\n                setConfigDetails(null);\n                setAvailableApiKeys([]);\n            } finally{\n                setIsLoading(false);\n                setIsLoadingApiKeys(false);\n                setShowOptimisticLoading(false);\n            }\n        }\n    }[\"RoutingSetupConfigPage.useCallback[fetchConfigAndKeys]\"], [\n        configId,\n        getCachedData,\n        isCached\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RoutingSetupConfigPage.useEffect\": ()=>{\n            fetchConfigAndKeys();\n        }\n    }[\"RoutingSetupConfigPage.useEffect\"], [\n        fetchConfigAndKeys\n    ]);\n    // Fetch complexity assignments for the selected API key\n    const fetchComplexityAssignmentsForKey = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RoutingSetupConfigPage.useCallback[fetchComplexityAssignmentsForKey]\": async (apiKeyId)=>{\n            if (!configId || !apiKeyId) return;\n            setIsFetchingAssignments(true);\n            setAssignmentsError(null);\n            setAssignmentsSuccessMessage(null);\n            try {\n                const response = await fetch(\"/api/custom-configs/\".concat(configId, \"/keys/\").concat(apiKeyId, \"/complexity-assignments\"));\n                if (!response.ok) {\n                    const errorData = await response.json();\n                    throw new Error(errorData.error || 'Failed to fetch complexity assignments');\n                }\n                const fetchedLevels = await response.json();\n                setComplexityAssignments({\n                    \"RoutingSetupConfigPage.useCallback[fetchComplexityAssignmentsForKey]\": (prev)=>({\n                            ...prev,\n                            [apiKeyId]: fetchedLevels\n                        })\n                }[\"RoutingSetupConfigPage.useCallback[fetchComplexityAssignmentsForKey]\"]);\n                setCurrentKeyComplexityLevels(fetchedLevels);\n            } catch (err) {\n                setAssignmentsError(\"Error fetching assignments for key: \".concat(err.message));\n                setCurrentKeyComplexityLevels([]); // Reset on error\n            } finally{\n                setIsFetchingAssignments(false);\n            }\n        }\n    }[\"RoutingSetupConfigPage.useCallback[fetchComplexityAssignmentsForKey]\"], [\n        configId\n    ]);\n    // Effect to fetch assignments when selectedApiKeyForComplexity changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"RoutingSetupConfigPage.useEffect\": ()=>{\n            if (selectedApiKeyForComplexity) {\n                fetchComplexityAssignmentsForKey(selectedApiKeyForComplexity);\n            } else {\n                // Clear levels if no key is selected\n                setCurrentKeyComplexityLevels([]);\n                setAssignmentsError(null);\n            }\n        }\n    }[\"RoutingSetupConfigPage.useEffect\"], [\n        selectedApiKeyForComplexity,\n        fetchComplexityAssignmentsForKey\n    ]);\n    // Handle checkbox change for complexity levels\n    const handleComplexityLevelChange = (level, checked)=>{\n        setCurrentKeyComplexityLevels((prevLevels)=>{\n            if (checked) {\n                return [\n                    ...prevLevels,\n                    level\n                ].sort((a, b)=>a - b);\n            } else {\n                return prevLevels.filter((l)=>l !== level);\n            }\n        });\n    };\n    // Save complexity assignments for the selected API key\n    const handleSaveComplexityAssignments = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"RoutingSetupConfigPage.useCallback[handleSaveComplexityAssignments]\": async ()=>{\n            if (!configId || !selectedApiKeyForComplexity) {\n                setAssignmentsError('No API key selected to save assignments for.');\n                return;\n            }\n            setIsSavingAssignments(true);\n            setAssignmentsError(null);\n            setAssignmentsSuccessMessage(null);\n            try {\n                const response = await fetch(\"/api/custom-configs/\".concat(configId, \"/keys/\").concat(selectedApiKeyForComplexity, \"/complexity-assignments\"), {\n                    method: 'PUT',\n                    headers: {\n                        'Content-Type': 'application/json'\n                    },\n                    body: JSON.stringify({\n                        complexity_levels: currentKeyComplexityLevels\n                    })\n                });\n                if (!response.ok) {\n                    const errorData = await response.json();\n                    throw new Error(errorData.error || 'Failed to save complexity assignments');\n                }\n                const result = await response.json();\n                // Update the main store of assignments for this key\n                setComplexityAssignments({\n                    \"RoutingSetupConfigPage.useCallback[handleSaveComplexityAssignments]\": (prev)=>({\n                            ...prev,\n                            [selectedApiKeyForComplexity]: [\n                                ...currentKeyComplexityLevels\n                            ]\n                        })\n                }[\"RoutingSetupConfigPage.useCallback[handleSaveComplexityAssignments]\"]);\n                setAssignmentsSuccessMessage(result.message || 'Complexity assignments saved successfully!');\n            } catch (err) {\n                setAssignmentsError(\"Error saving assignments: \".concat(err.message));\n            } finally{\n                setIsSavingAssignments(false);\n            }\n        }\n    }[\"RoutingSetupConfigPage.useCallback[handleSaveComplexityAssignments]\"], [\n        configId,\n        selectedApiKeyForComplexity,\n        currentKeyComplexityLevels\n    ]);\n    // Handle drag and drop reordering for strict fallback\n    const handleDragReorder = (newOrder)=>{\n        setOrderedFallbackKeys(newOrder);\n        // Update strategyParams immediately for saving\n        setStrategyParams({\n            ordered_api_key_ids: newOrder.map((k)=>k.id)\n        });\n    };\n    const moveFallbackKey = (index, direction)=>{\n        const newOrderedKeys = [\n            ...orderedFallbackKeys\n        ];\n        const keyToMove = newOrderedKeys[index];\n        if (direction === 'up' && index > 0) {\n            newOrderedKeys.splice(index, 1);\n            newOrderedKeys.splice(index - 1, 0, keyToMove);\n        } else if (direction === 'down' && index < newOrderedKeys.length - 1) {\n            newOrderedKeys.splice(index, 1);\n            newOrderedKeys.splice(index + 1, 0, keyToMove);\n        }\n        setOrderedFallbackKeys(newOrderedKeys);\n        // Update strategyParams immediately for saving\n        setStrategyParams({\n            ordered_api_key_ids: newOrderedKeys.map((k)=>k.id)\n        });\n    };\n    const handleSaveRoutingSettings = async (e)=>{\n        e.preventDefault();\n        if (!configId || !configDetails) {\n            setError('Configuration details not loaded.');\n            return;\n        }\n        setIsLoading(true);\n        setError(null);\n        setSuccessMessage(null);\n        let paramsToSave = strategyParams;\n        if (selectedStrategy === 'strict_fallback') {\n            paramsToSave = {\n                ordered_api_key_ids: orderedFallbackKeys.map((k)=>k.id)\n            };\n        }\n        // Add similar logic for other strategies to structure their paramsToSave\n        try {\n            const response = await fetch(\"/api/custom-configs/\".concat(configId, \"/routing\"), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    routing_strategy: selectedStrategy,\n                    routing_strategy_params: paramsToSave\n                })\n            });\n            if (!response.ok) {\n                const errorData = await response.json();\n                throw new Error(errorData.error || 'Failed to save routing settings');\n            }\n            const result = await response.json();\n            setSuccessMessage(result.message || 'Routing settings saved successfully!');\n            setConfigDetails((prev)=>prev ? {\n                    ...prev,\n                    routing_strategy: selectedStrategy,\n                    routing_strategy_params: paramsToSave\n                } : null);\n            // Update local params to reflect saved state if necessary, e.g. if backend transforms it\n            setStrategyParams(paramsToSave);\n        } catch (err) {\n            setError(\"Error saving settings: \".concat(err.message));\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const renderComplexityAssignmentUI = ()=>{\n        var _availableApiKeys_find;\n        if (selectedStrategy !== 'complexity_round_robin') return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mt-8 pt-6 border-t border-gray-200\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"text-xl font-semibold text-gray-900 mb-4\",\n                    children: \"Complexity-Based Key Assignments\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 584,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-sm text-gray-600 mb-6\",\n                    children: \"Assign API keys to handle prompts of specific complexity levels (1-5). The system will classify incoming prompts and round-robin requests among keys assigned to that complexity.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 585,\n                    columnNumber: 9\n                }, this),\n                isLoadingApiKeys && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center py-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 591,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm text-gray-600 ml-2\",\n                            children: \"Loading API keys...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 592,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 590,\n                    columnNumber: 11\n                }, this),\n                !isLoadingApiKeys && availableApiKeys.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-yellow-50 border border-yellow-200 rounded-lg p-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm text-yellow-800\",\n                        children: \"No API keys found for this configuration. Please add API keys first on the model configuration page.\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 597,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 596,\n                    columnNumber: 11\n                }, this),\n                availableApiKeys.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            htmlFor: \"apiKeyForComplexity\",\n                            className: \"block text-sm font-medium text-gray-700 mb-2\",\n                            children: \"Select API Key to Assign Complexities:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 603,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                            id: \"apiKeyForComplexity\",\n                            value: selectedApiKeyForComplexity || '',\n                            onChange: (e)=>setSelectedApiKeyForComplexity(e.target.value || null),\n                            className: \"form-select max-w-md\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                    value: \"\",\n                                    disabled: true,\n                                    children: \"-- Select an API Key --\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 610,\n                                    columnNumber: 15\n                                }, this),\n                                availableApiKeys.map((key)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                        value: key.id,\n                                        children: [\n                                            key.label,\n                                            \" (\",\n                                            key.provider,\n                                            \" - \",\n                                            key.predefined_model_id,\n                                            \")\"\n                                        ]\n                                    }, key.id, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 612,\n                                        columnNumber: 17\n                                    }, this))\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 604,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 602,\n                    columnNumber: 11\n                }, this),\n                selectedApiKeyForComplexity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                            className: \"text-md font-medium text-gray-900 mb-4\",\n                            children: [\n                                \"Assign Complexity Levels for: \",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-orange-600\",\n                                    children: (_availableApiKeys_find = availableApiKeys.find((k)=>k.id === selectedApiKeyForComplexity)) === null || _availableApiKeys_find === void 0 ? void 0 : _availableApiKeys_find.label\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 620,\n                                    columnNumber: 98\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 620,\n                            columnNumber: 13\n                        }, this),\n                        isFetchingAssignments && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center py-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"animate-spin rounded-full h-5 w-5 border-b-2 border-orange-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 624,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm text-gray-600 ml-2\",\n                                    children: \"Loading current assignments...\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 625,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 623,\n                            columnNumber: 15\n                        }, this),\n                        assignmentsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-red-50 border border-red-200 rounded-lg p-3 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-red-800 text-sm\",\n                                children: assignmentsError\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 631,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 630,\n                            columnNumber: 15\n                        }, this),\n                        assignmentsSuccessMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-green-50 border border-green-200 rounded-lg p-3 mb-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-green-800 text-sm\",\n                                children: assignmentsSuccessMessage\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 637,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 636,\n                            columnNumber: 15\n                        }, this),\n                        !isFetchingAssignments && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3 mb-6\",\n                            children: [\n                                1,\n                                2,\n                                3,\n                                4,\n                                5\n                            ].map((level)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center space-x-3 p-3 bg-gray-50 border border-gray-200 rounded-lg hover:border-gray-300 cursor-pointer transition-colors duration-200\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"checkbox\",\n                                            checked: currentKeyComplexityLevels.includes(level),\n                                            onChange: (e)=>handleComplexityLevelChange(level, e.target.checked),\n                                            className: \"h-4 w-4 text-orange-600 border-gray-300 rounded focus:ring-orange-500 focus:ring-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 645,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-gray-900\",\n                                            children: [\n                                                \"Complexity Level \",\n                                                level\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 651,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, level, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 644,\n                                    columnNumber: 19\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 642,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleSaveComplexityAssignments,\n                            disabled: isSavingAssignments || isFetchingAssignments,\n                            className: \"btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                            children: isSavingAssignments ? 'Saving Assignments...' : 'Save Assignments for this Key'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 657,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 619,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n            lineNumber: 583,\n            columnNumber: 7\n        }, this);\n    };\n    // Render configuration content based on selected strategy\n    const renderConfigurationContent = ()=>{\n        const selectedStrategyData = ROUTING_STRATEGIES.find((s)=>s.id === selectedStrategy);\n        if (selectedStrategy === 'none') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-16\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-20 h-20 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-10 h-10 text-orange-600\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 678,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 677,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-2xl font-bold text-gray-900 mb-4\",\n                        children: \"Default Behavior\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 680,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 max-w-md mx-auto leading-relaxed\",\n                        children: selectedStrategyData === null || selectedStrategyData === void 0 ? void 0 : selectedStrategyData.description\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 681,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-8 p-4 bg-green-50 border border-green-200 rounded-xl\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center space-x-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                    className: \"w-5 h-5 text-green-600\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 686,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-green-800 font-medium\",\n                                    children: \"No additional setup required\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 687,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 685,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 684,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 pt-6 border-t border-gray-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                            disabled: isLoading,\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2 animate-spin\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 701,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 700,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Saving...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 707,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Save Routing Settings\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 693,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 692,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                lineNumber: 676,\n                columnNumber: 9\n            }, this);\n        }\n        if (selectedStrategy === 'intelligent_role') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"w-7 h-7 mr-3 text-orange-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 722,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Intelligent Role Routing\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 721,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 leading-relaxed\",\n                                children: selectedStrategyData === null || selectedStrategyData === void 0 ? void 0 : selectedStrategyData.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 725,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 720,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-xl p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-blue-900 mb-3\",\n                                        children: \"How it works:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 732,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 text-sm text-blue-800\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-bold text-blue-600\",\n                                                            children: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 736,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 735,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"System analyzes your prompt to understand the main task\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 738,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 734,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-bold text-blue-600\",\n                                                            children: \"2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 742,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 741,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Matches task to relevant RoKey operational roles (e.g., 'Coding - Frontend', 'Copywriting')\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 744,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 740,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-bold text-blue-600\",\n                                                            children: \"3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 748,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 747,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Routes to assigned API key or falls back to 'Default General Chat Model'\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 750,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 746,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 733,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 731,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 border border-green-200 rounded-xl p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-6 h-6 text-green-600 mt-0.5 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 757,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-green-900 mb-2\",\n                                                    children: \"Ready to use!\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 759,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-green-800 leading-relaxed\",\n                                                    children: \"No additional setup required. Future enhancements may allow further customization.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 760,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 758,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 756,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 755,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 730,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 pt-6 border-t border-gray-200 flex justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                            disabled: isLoading,\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2 animate-spin\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 778,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 777,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Saving...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 784,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Save Routing Settings\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 770,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 769,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                lineNumber: 719,\n                columnNumber: 9\n            }, this);\n        }\n        if (selectedStrategy === 'strict_fallback') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"w-7 h-7 mr-3 text-orange-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 799,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Strict Fallback Configuration\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 798,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 leading-relaxed\",\n                                children: selectedStrategyData === null || selectedStrategyData === void 0 ? void 0 : selectedStrategyData.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 802,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 797,\n                        columnNumber: 11\n                    }, this),\n                    isLoadingApiKeys && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 border-2 border-orange-600/20 border-t-orange-600 rounded-full animate-spin\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 809,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 ml-3\",\n                                children: \"Loading API keys...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 810,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 808,\n                        columnNumber: 13\n                    }, this),\n                    !isLoadingApiKeys && availableApiKeys.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-yellow-50 border border-yellow-200 rounded-xl p-8 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-8 h-8 text-yellow-600\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 818,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 817,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 816,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                className: \"text-lg font-semibold text-yellow-900 mb-2\",\n                                children: \"No API Keys Found\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 821,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-yellow-800 leading-relaxed\",\n                                children: \"Please add API keys on the main configuration page to set up fallback order.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 822,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 815,\n                        columnNumber: 13\n                    }, this),\n                    !isLoadingApiKeys && availableApiKeys.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-xl p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4 text-blue-600\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 834,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 833,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 832,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-blue-800 leading-relaxed\",\n                                            children: \"Drag and drop to arrange the API keys in the desired order of execution. The router will try the first key, then the second if the first fails, and so on.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 837,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 831,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 830,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_16__.ReorderGroup, {\n                                axis: \"y\",\n                                values: orderedFallbackKeys,\n                                onReorder: handleDragReorder,\n                                className: \"space-y-3\",\n                                children: orderedFallbackKeys.map((apiKey, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DraggableApiKeyItem, {\n                                        apiKey: apiKey,\n                                        index: index\n                                    }, apiKey.id, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 851,\n                                        columnNumber: 19\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 844,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 829,\n                        columnNumber: 13\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 pt-6 border-t border-gray-200 flex justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                            disabled: isLoading || availableApiKeys.length === 0,\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2 animate-spin\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 871,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 870,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Saving...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 877,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Save Routing Settings\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 863,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 862,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                lineNumber: 796,\n                columnNumber: 9\n            }, this);\n        }\n        if (selectedStrategy === 'complexity_round_robin') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"w-7 h-7 mr-3 text-orange-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 892,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Complexity-Based Round-Robin\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 891,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 leading-relaxed\",\n                                children: selectedStrategyData === null || selectedStrategyData === void 0 ? void 0 : selectedStrategyData.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 895,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 890,\n                        columnNumber: 11\n                    }, this),\n                    renderComplexityAssignmentUI(),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 pt-6 border-t border-gray-200 flex justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                            disabled: isLoading,\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2 animate-spin\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 912,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 911,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Saving...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 918,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Save Routing Settings\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 904,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 903,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                lineNumber: 889,\n                columnNumber: 9\n            }, this);\n        }\n        if (selectedStrategy === 'cost_optimized') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-gray-900 mb-4 flex items-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"w-7 h-7 mr-3 text-orange-600\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 933,\n                                        columnNumber: 15\n                                    }, this),\n                                    \"Cost-Optimized Routing\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 932,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 leading-relaxed\",\n                                children: selectedStrategyData === null || selectedStrategyData === void 0 ? void 0 : selectedStrategyData.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 936,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 931,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-green-50 border border-green-200 rounded-xl p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-semibold text-green-900 mb-3\",\n                                        children: \"How it works:\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 943,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-3 text-sm text-green-800\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-bold text-green-600\",\n                                                            children: \"1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 947,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 946,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"System analyzes token pricing for all available models in your configuration\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 949,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 945,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-bold text-green-600\",\n                                                            children: \"2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 953,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 952,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Automatically routes requests to the cheapest available model\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 955,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 951,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"w-6 h-6 bg-green-100 rounded-full flex items-center justify-center mt-0.5 flex-shrink-0\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs font-bold text-green-600\",\n                                                            children: \"3\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                            lineNumber: 959,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 958,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        children: \"Falls back to next cheapest option if the primary choice fails\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 961,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 957,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 944,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 942,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-blue-50 border border-blue-200 rounded-xl p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"w-6 h-6 text-blue-600 mt-0.5 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 968,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-blue-900 mb-2\",\n                                                    children: \"Maximize Cost Savings\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 970,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-blue-800 leading-relaxed\",\n                                                    children: \"This strategy prioritizes cost efficiency by always selecting the most economical model available. Perfect for high-volume usage where cost optimization is critical.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 971,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 969,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 967,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 966,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-orange-50 border border-orange-200 rounded-xl p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start space-x-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"w-6 h-6 text-orange-600 mt-0.5 flex-shrink-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 981,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-semibold text-orange-900 mb-2\",\n                                                    children: \"Ready to use!\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 983,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-orange-800 leading-relaxed\",\n                                                    children: \"No additional setup required. RouKey will automatically determine the most cost-effective routing based on your configured API keys.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 984,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 982,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 980,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 979,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 941,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12 pt-6 border-t border-gray-200 flex justify-end\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            className: \"btn-primary disabled:opacity-50 disabled:cursor-not-allowed\",\n                            disabled: isLoading,\n                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4 mr-2 animate-spin\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1002,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1001,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Saving...\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                        className: \"w-4 h-4 mr-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1008,\n                                        columnNumber: 19\n                                    }, this),\n                                    \"Save Routing Settings\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 994,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 993,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                lineNumber: 930,\n                columnNumber: 9\n            }, this);\n        }\n        return null;\n    };\n    // Main render logic with optimistic loading\n    if (showOptimisticLoading && !isCached(configId)) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RoutingSetupLoadingSkeleton__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n            lineNumber: 1023,\n            columnNumber: 12\n        }, this);\n    }\n    if (isLoading && !configDetails) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_RoutingSetupLoadingSkeleton__WEBPACK_IMPORTED_MODULE_5__.CompactRoutingSetupLoadingSkeleton, {}, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n            lineNumber: 1027,\n            columnNumber: 12\n        }, this);\n    }\n    if (error && !configDetails && !isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-3xl font-bold text-gray-900 mb-6\",\n                    children: \"Routing Setup Error\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1033,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card border-red-200 bg-red-50 p-6\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-2 h-2 bg-red-500 rounded-full\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1036,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-red-800\",\n                                    children: error\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1037,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1035,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                            href: \"/my-models\",\n                            className: \"mt-4 btn-primary inline-block\",\n                            children: \"Back to My Models\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                            lineNumber: 1039,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1034,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n            lineNumber: 1032,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-cream\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-6 py-8\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleBackNavigation,\n                                    className: \"btn-secondary inline-flex items-center text-sm\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"w-4 h-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1058,\n                                            columnNumber: 17\n                                        }, this),\n                                        getBackButtonText()\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1054,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1053,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-h1 text-gray-900\",\n                                        children: \"Advanced Routing Setup\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1063,\n                                        columnNumber: 15\n                                    }, this),\n                                    configDetails && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-body-sm text-gray-600 mt-1\",\n                                        children: [\n                                            \"Configuration: \",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-orange-600 font-semibold\",\n                                                children: configDetails.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                lineNumber: 1068,\n                                                columnNumber: 34\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1067,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1062,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1052,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1051,\n                    columnNumber: 9\n                }, this),\n                error && !successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card border-red-200 bg-red-50 p-6 mb-8 max-w-4xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-red-100 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 text-red-600\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1081,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1080,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1079,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-red-800\",\n                                        children: \"Configuration Error\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1085,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-red-700 mt-1\",\n                                        children: error\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1086,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1084,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1078,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1077,\n                    columnNumber: 11\n                }, this),\n                successMessage && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card border-green-200 bg-green-50 p-6 mb-8 max-w-4xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center space-x-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-8 h-8 bg-green-100 rounded-full flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-4 h-4 text-green-600\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M5 13l4 4L19 7\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1097,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1096,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1095,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-semibold text-green-800\",\n                                        children: \"Settings Saved\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1101,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-green-700 mt-1\",\n                                        children: successMessage\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1102,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1100,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1094,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1093,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"card p-6 sticky top-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-bold text-gray-900 mb-6\",\n                                            children: \"Routing Strategy\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1114,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-3\",\n                                            children: ROUTING_STRATEGIES.map((strategy)=>{\n                                                const IconComponent = strategy.icon;\n                                                const isSelected = selectedStrategy === strategy.id;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>{\n                                                        setSelectedStrategy(strategy.id);\n                                                        // Reset/initialize params based on new strategy\n                                                        if (strategy.id === 'strict_fallback') {\n                                                            const existingFallbackParams = strategyParams.ordered_api_key_ids;\n                                                            if (existingFallbackParams && Array.isArray(existingFallbackParams)) {\n                                                                const reordered = existingFallbackParams.map((id)=>availableApiKeys.find((k)=>k.id === id)).filter(Boolean);\n                                                                const remaining = availableApiKeys.filter((k)=>!existingFallbackParams.includes(k.id));\n                                                                setOrderedFallbackKeys([\n                                                                    ...reordered,\n                                                                    ...remaining\n                                                                ]);\n                                                            } else {\n                                                                setOrderedFallbackKeys([\n                                                                    ...availableApiKeys\n                                                                ]);\n                                                            }\n                                                            setStrategyParams({\n                                                                ordered_api_key_ids: orderedFallbackKeys.map((k)=>k.id)\n                                                            });\n                                                        } else {\n                                                            setStrategyParams({});\n                                                            setOrderedFallbackKeys([\n                                                                ...availableApiKeys\n                                                            ]);\n                                                        }\n                                                        // Reset complexity assignment states if strategy changes\n                                                        setSelectedApiKeyForComplexity(null);\n                                                        setCurrentKeyComplexityLevels([]);\n                                                        setAssignmentsError(null);\n                                                        setAssignmentsSuccessMessage(null);\n                                                    },\n                                                    disabled: isLoading,\n                                                    className: \"w-full text-left p-4 rounded-xl border-2 transition-all duration-300 group \".concat(isSelected ? 'border-orange-500 bg-orange-50 shadow-lg transform scale-[1.02]' : 'border-gray-200 bg-white hover:border-orange-300 hover:bg-orange-25 hover:shadow-md'),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-start space-x-4\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"p-3 rounded-lg transition-colors duration-300 \".concat(isSelected ? 'bg-orange-100 text-orange-600' : 'bg-gray-100 text-gray-600 group-hover:bg-orange-100 group-hover:text-orange-600'),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                    className: \"w-6 h-6\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                                    lineNumber: 1159,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1154,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1 min-w-0\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center space-x-2 mb-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"font-semibold text-sm transition-colors duration-300 \".concat(isSelected ? 'text-orange-900' : 'text-gray-900'),\n                                                                                children: strategy.name\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1163,\n                                                                                columnNumber: 31\n                                                                            }, this),\n                                                                            isSelected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowDownIcon_ArrowLeftIcon_ArrowUpIcon_Bars3Icon_BoltIcon_CheckCircleIcon_CircleStackIcon_Cog6ToothIcon_CurrencyDollarIcon_ListBulletIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                className: \"w-4 h-4 text-orange-600 animate-in fade-in duration-300\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                                                lineNumber: 1169,\n                                                                                columnNumber: 33\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1162,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs leading-relaxed transition-colors duration-300 \".concat(isSelected ? 'text-orange-700' : 'text-gray-600'),\n                                                                        children: strategy.shortDescription\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                                        lineNumber: 1172,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                                lineNumber: 1161,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                        lineNumber: 1153,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, strategy.id, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                                    lineNumber: 1121,\n                                                    columnNumber: 23\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1115,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1113,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1112,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                                    onSubmit: handleSaveRoutingSettings,\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"card p-8 min-h-[600px]\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"animate-in fade-in slide-in-from-right-4 duration-500\",\n                                            children: renderConfigurationContent()\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                            lineNumber: 1191,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                        lineNumber: 1189,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                    lineNumber: 1188,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                                lineNumber: 1187,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                        lineNumber: 1110,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n                    lineNumber: 1109,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n            lineNumber: 1049,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\app\\\\routing-setup\\\\[configId]\\\\page.tsx\",\n        lineNumber: 1048,\n        columnNumber: 5\n    }, this);\n}\n_s(RoutingSetupConfigPage, \"Dz2Ft8UPkcwJ1nKHkSREVZZnayw=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        _hooks_useRoutingSetupPrefetch__WEBPACK_IMPORTED_MODULE_4__.useRoutingSetupPrefetch\n    ];\n});\n_c2 = RoutingSetupConfigPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"DraggableApiKeyItem\");\n$RefreshReg$(_c1, \"SimpleApiKeyItem\");\n$RefreshReg$(_c2, \"RoutingSetupConfigPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/routing-setup/[configId]/page.tsx\n"));

/***/ })

});