"use client";import D,{createContext as k,useContext as v,useMemo as T,useState as _}from"react";import{useEvent as P}from'../../hooks/use-event.js';import{useId as A}from'../../hooks/use-id.js';import{useIsoMorphicEffect as B}from'../../hooks/use-iso-morphic-effect.js';import{useSyncRefs as F}from'../../hooks/use-sync-refs.js';import{useDisabled as M}from'../../internal/disabled.js';import{useProvidedId as S}from'../../internal/id.js';import*as m from'../../utils/dom.js';import{forwardRefWithAs as I,useRender as H}from'../../utils/render.js';let L=k(null);L.displayName="LabelContext";function C(){let n=v(L);if(n===null){let l=new Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(l,C),l}return n}function N(n){var a,e,o;let l=(e=(a=v(L))==null?void 0:a.value)!=null?e:void 0;return((o=n==null?void 0:n.length)!=null?o:0)>0?[l,...n].filter(Boolean).join(" "):l}function Q({inherit:n=!1}={}){let l=N(),[a,e]=_([]),o=n?[l,...a].filter(Boolean):a;return[o.length>0?o.join(" "):void 0,T(()=>function(t){let p=P(i=>(e(u=>[...u,i]),()=>e(u=>{let d=u.slice(),f=d.indexOf(i);return f!==-1&&d.splice(f,1),d}))),b=T(()=>({register:p,slot:t.slot,name:t.name,props:t.props,value:t.value}),[p,t.slot,t.name,t.props,t.value]);return D.createElement(L.Provider,{value:b},t.children)},[e])]}let G="label";function U(n,l){var E;let a=A(),e=C(),o=S(),y=M(),{id:t=`headlessui-label-${a}`,htmlFor:p=o!=null?o:(E=e.props)==null?void 0:E.htmlFor,passive:b=!1,...i}=n,u=F(l);B(()=>e.register(t),[t,e.register]);let d=P(s=>{let g=s.currentTarget;if(!(s.target!==s.currentTarget&&m.isInteractiveElement(s.target))&&(m.isHTMLLabelElement(g)&&s.preventDefault(),e.props&&"onClick"in e.props&&typeof e.props.onClick=="function"&&e.props.onClick(s),m.isHTMLLabelElement(g))){let r=document.getElementById(g.htmlFor);if(r){let x=r.getAttribute("disabled");if(x==="true"||x==="")return;let h=r.getAttribute("aria-disabled");if(h==="true"||h==="")return;(m.isHTMLInputElement(r)&&(r.type==="file"||r.type==="radio"||r.type==="checkbox")||r.role==="radio"||r.role==="checkbox"||r.role==="switch")&&r.click(),r.focus({preventScroll:!0})}}}),f=y||!1,R=T(()=>({...e.slot,disabled:f}),[e.slot,f]),c={ref:u,...e.props,id:t,htmlFor:p,onClick:d};return b&&("onClick"in c&&(delete c.htmlFor,delete c.onClick),"onClick"in i&&delete i.onClick),H()({ourProps:c,theirProps:i,slot:R,defaultTag:p?G:"div",name:e.name||"Label"})}let j=I(U),V=Object.assign(j,{});export{V as Label,C as useLabelContext,N as useLabelledBy,Q as useLabels};
